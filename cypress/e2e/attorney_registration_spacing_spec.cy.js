describe('Attorney Registration Form Layout', () => {
    it('should have proper spacing between Role dropdown and Hourly Rate input', () => {
        cy.visit('http://localhost:5000/register/attorney');

        // Ensure both elements are visible
        cy.get('select#role').should('be.visible');
        cy.get('input#hourly_rate').should('be.visible');

        // Get positions and dimensions
        cy.get('select#role').then($role => {
            const roleRect = $role[0].getBoundingClientRect();

            cy.get('input#hourly_rate').then($rate => {
                const rateRect = $rate[0].getBoundingClientRect();

                const verticalGap = rateRect.top - roleRect.bottom;

                // Assert minimum vertical spacing (e.g., >10px)
                expect(verticalGap).to.be.greaterThan(10);
            });
        });
    });
});
