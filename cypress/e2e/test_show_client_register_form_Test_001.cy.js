describe('Client Registration Page', () => {
    it('should render the client registration form when visiting /register/client', () => {
        // Visit the client registration page 
        cy.visit('http://localhost:5000/register/client');

        // Verify page heading
        cy.contains('h1', 'Register as a Client').should('be.visible');

        // Check form inputs
        cy.get('form#clientRegistrationForm').within(() => {
            cy.get('input[name="name"]').should('be.visible');
            cy.get('input[name="username"]').should('be.visible');
            cy.get('input[name="password"]').should('be.visible');
            cy.get('input[name="email"]').should('be.visible');
            cy.get('input[name="phone_number"]').should('be.visible');
            cy.get('input[name="address"]').should('be.visible');
            cy.get('textarea[name="description"]').should('be.visible');
            cy.get('button[type="submit"]').should('contain', 'Register');
        });
    });
});
