kind: pipeline
name: full-ci-pipeline
type: docker

trigger:
  event:
    - push
    - pull_request
  branch:
    - main
    - dev
    - review/**

services:
  - name: mysql
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: case_management_db
      MYSQL_USER: case_user
      MYSQL_PASSWORD: case_password
    ports:
      - 3306

steps:
  - name: checkout
    image: alpine/git
    commands:
      - git clone ${DRONE_REPO_LINK} .

  - name: setup-python
    image: python:3.10
    commands:
      - pip install --upgrade pip
      - pip install -r requirements.txt

  - name: setup-node
    image: node:18
    commands:
      - npm install -g newman

  - name: wait-for-mysql
    image: alpine
    commands:
      - echo "Waiting for MySQL..."
      - sleep 15

  - name: export-env-vars
    image: alpine
    commands:
      - echo "DB_USERNAME=case_user" >> /etc/environment
      - echo "DB_PASSWORD=case_password" >> /etc/environment
      - echo "DB_HOST=mysql" >> /etc/environment
      - echo "DB_NAME=case_management_db" >> /etc/environment
      - echo "FLASK_APP=case_management:create_app" >> /etc/environment
      - echo "DATABASE_URL=mysql+pymysql://case_user:case_password@mysql/case_management_db" >> /etc/environment

  - name: db-stamp
    image: python:3.10
    commands:
      - flask db stamp head

  - name: db-init
    image: python:3.10
    commands:
      - python ci_init_db.py

  - name: backend-pytest
    image: python:3.10
    environment:
      CI: "true"
    commands:
      - pytest --cov=. --cov-report=term --ignore=case_management/archived_tests

  - name: flask-serve
    image: python:3.10
    commands:
      - pip install -r requirements.txt
      - python -m flask run --host=0.0.0.0 --port=5000 &
      - sleep 10

  - name: postman-api-tests
    image: node:18
    commands:
      - newman run ./postman/collections/sample.postman_collection.json

  - name: install-frontend-deps
    image: node:18
    commands:
      - npm install

  - name: cypress-tests
    image: cypress/browsers:node18.12.0-chrome107
    environment:
      CYPRESS_baseUrl: http://localhost:5000
    commands:
      - DEBUG=cypress:* npx cypress run

  - name: android-tests
    image: openjdk:17
    commands:
      - if [ ! -f ./gradlew ]; then echo "No Android project"; exit 0; fi
      - chmod +x ./gradlew
      - ./gradlew assembleDebug
      - ./gradlew testDebugUnitTest

  - name: upload-review-report
    image: python:3.10
    environment:
      CODESCAN_AUTH:
        from_secret: codscan_auth
      PR_BODY: ${DRONE_PULL_REQUEST_BODY}
      BRANCH_NAME: ${DRONE_SOURCE_BRANCH}
    commands:
      - pip install openai
      - python .github/scripts/check_quality.py
      - bash .github/scripts/push_review_to_private_repo.sh
