document.addEventListener('DOMContentLoaded', function () {
    // Get references to the attorney registration form and message container
    const form = document.getElementById('attorneyRegistrationForm');
    const messageContainer = document.getElementById('registration_message');

    // Listen for form submission
    form.addEventListener('submit', async function (e) {
        e.preventDefault(); // Prevent the default form submission

        // Convert the form data into a JSON object
        const formData = new FormData(form);
        let payload = {};
        formData.forEach((value, key) => {
            payload[key] = value;
        });

        try {
            const lawFirmInput = payload.law_firm_name;
            let lawFirmResponse;

            // ✅ Use different validation depending on input type
            if (!isNaN(lawFirmInput)) {
                // It's a numeric ID
                lawFirmResponse = await fetch(`/validate-law-firm-id/${lawFirmInput}`);
            } else {
                // It's a name
                lawFirmResponse = await fetch(`/validate-law-firm/${encodeURIComponent(lawFirmInput)}`);
            }

            // Handle response errors
            if (lawFirmResponse.status === 404) {
                throw new Error("Invalid law firm. Please enter a valid law firm name or ID.");
            } else if (!lawFirmResponse.ok) {
                const errData = await lawFirmResponse.json();
                throw new Error(errData.error || "Unexpected error during law firm validation.");
            }

            const lawFirmData = await lawFirmResponse.json();
            payload.law_firm_id = lawFirmData.law_firm_id;  // Use validated ID
            delete payload.law_firm_name;  // Optional: remove raw input if backend doesn't expect it

            // Send the POST request to the attorney registration route
            const response = await fetch(form.action, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });

            const result = await response.json();

            if (response.ok) {
                messageContainer.textContent = result.message || 'Attorney registered successfully!';
                messageContainer.className = 'success-message';
                form.reset(); // Reset the form after successful submission
            } else {
                messageContainer.textContent = result.error || 'An error occurred during registration.';
                messageContainer.className = 'error-message';
            }
        } catch (error) {
            messageContainer.textContent = error.message || 'An unexpected error occurred.';
            messageContainer.className = 'error-message';
            console.error('Error during attorney registration:', error);
        }
    });
});
