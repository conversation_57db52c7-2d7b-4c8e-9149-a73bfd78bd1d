/* static/css/styles.css */
/* Autocomplete Suggestions */
.autocomplete-suggestions {
    border: 1px solid #ccc;
    max-height: 150px;
    overflow-y: auto;
    position: absolute;
    background: #fff;
    z-index: 1000;
  }
  
  .autocomplete-suggestion {
    padding: 5px;
    cursor: pointer;
  }
  
  .autocomplete-suggestion:hover {
    background: #f0f0f0;
  }



body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f4f4f9;
}

form {
    width: 50%;
    margin: auto;
    padding: 20px;
    background: white;
    border: 1px solid #ccc;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-container {
    max-width: 500px;
    margin: auto;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 8px;
    background-color: #f9f9f9;
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
}

input, textarea, button, select {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

button {
    background-color: #007bff;
    color: white;
    font-size: 16px;
    cursor: pointer;
}

button:hover {
    background-color: #0056b3;
}

.success-message {
    color: green;
    font-weight: bold;
    margin-top: 10px;
}

.error-message {
    color: red;
    font-weight: bold;
    margin-top: 10px;
}


.total-amount {
    margin-top: 15px;
    font-weight: bold;
}