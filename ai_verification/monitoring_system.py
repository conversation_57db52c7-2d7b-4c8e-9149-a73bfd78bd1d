"""
AI Model Continuous Monitoring System
=====================================

This module provides continuous monitoring capabilities for deployed AI models,
including performance tracking, data drift detection, and automated alerting.
"""

import logging
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import sqlite3
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
import requests
from pathlib import Path
import threading
import time
from scipy import stats

from verification_framework import VerificationStatus, ModelType, VerificationResult

logger = logging.getLogger(__name__)

@dataclass
class MonitoringMetric:
    """Container for monitoring metrics"""
    metric_name: str
    value: float
    threshold: float
    status: str
    timestamp: datetime
    model_version: str

@dataclass
class DataDriftResult:
    """Container for data drift detection results"""
    drift_detected: bool
    drift_score: float
    affected_features: List[str]
    timestamp: datetime

class ContinuousMonitoringSystem:
    """
    Continuous monitoring system for AI models in production
    """
    
    def __init__(self, config_path: str = "ai_verification/config.json"):
        """Initialize the monitoring system"""
        self.config = self._load_config(config_path)
        self.db_path = "ai_verification/monitoring.db"
        self.monitoring_active = False
        self.monitoring_thread = None
        self._init_database()
        
    def _load_config(self, config_path: str) -> Dict:
        """Load monitoring configuration"""
        with open(config_path, 'r') as f:
            return json.load(f)
    
    def _init_database(self):
        """Initialize monitoring database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create monitoring metrics table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS monitoring_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_type TEXT NOT NULL,
                model_version TEXT NOT NULL,
                metric_name TEXT NOT NULL,
                metric_value REAL NOT NULL,
                threshold_value REAL NOT NULL,
                status TEXT NOT NULL,
                timestamp DATETIME NOT NULL
            )
        ''')
        
        # Create data drift table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS data_drift_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_type TEXT NOT NULL,
                model_version TEXT NOT NULL,
                drift_detected BOOLEAN NOT NULL,
                drift_score REAL NOT NULL,
                affected_features TEXT,
                timestamp DATETIME NOT NULL
            )
        ''')
        
        # Create alerts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                alert_type TEXT NOT NULL,
                severity TEXT NOT NULL,
                message TEXT NOT NULL,
                model_type TEXT NOT NULL,
                model_version TEXT NOT NULL,
                resolved BOOLEAN DEFAULT FALSE,
                timestamp DATETIME NOT NULL
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def start_monitoring(self, model_type: ModelType, model_version: str):
        """Start continuous monitoring for a specific model"""
        
        if self.monitoring_active:
            logger.warning("Monitoring already active")
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(model_type, model_version),
            daemon=True
        )
        self.monitoring_thread.start()
        logger.info(f"Started monitoring for {model_type.value} v{model_version}")
    
    def stop_monitoring(self):
        """Stop continuous monitoring"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join()
        logger.info("Monitoring stopped")
    
    def _monitoring_loop(self, model_type: ModelType, model_version: str):
        """Main monitoring loop"""
        
        interval_hours = self.config["monitoring"]["monitoring_interval_hours"]
        interval_seconds = interval_hours * 3600
        
        while self.monitoring_active:
            try:
                # Collect current metrics
                current_metrics = self._collect_model_metrics(model_type, model_version)
                
                # Check for performance degradation
                self._check_performance_degradation(current_metrics, model_type, model_version)
                
                # Check for data drift
                drift_result = self._detect_data_drift(model_type, model_version)
                self._log_data_drift(drift_result, model_type, model_version)
                
                # Check for bias increases
                self._check_bias_increase(model_type, model_version)
                
                # Store metrics
                self._store_metrics(current_metrics, model_type, model_version)
                
                # Sleep until next monitoring cycle
                time.sleep(interval_seconds)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {str(e)}")
                time.sleep(60)  # Wait 1 minute before retrying
    
    def _collect_model_metrics(self, model_type: ModelType, model_version: str) -> Dict[str, float]:
        """Collect current model performance metrics"""
        
        # In practice, this would collect real metrics from your model serving infrastructure
        # For demonstration, we'll simulate metric collection
        
        metrics = {
            "accuracy": np.random.normal(0.85, 0.02),
            "precision": np.random.normal(0.82, 0.03),
            "recall": np.random.normal(0.80, 0.025),
            "f1_score": np.random.normal(0.81, 0.02),
            "latency_ms": np.random.normal(150, 20),
            "throughput_rps": np.random.normal(100, 10),
            "error_rate": np.random.normal(0.02, 0.005)
        }
        
        # Ensure metrics are within reasonable bounds
        metrics["accuracy"] = max(0, min(1, metrics["accuracy"]))
        metrics["precision"] = max(0, min(1, metrics["precision"]))
        metrics["recall"] = max(0, min(1, metrics["recall"]))
        metrics["f1_score"] = max(0, min(1, metrics["f1_score"]))
        metrics["error_rate"] = max(0, min(1, metrics["error_rate"]))
        
        return metrics
    
    def _check_performance_degradation(self, 
                                     current_metrics: Dict[str, float],
                                     model_type: ModelType,
                                     model_version: str):
        """Check for performance degradation"""
        
        thresholds = self.config["monitoring"]["alert_thresholds"]
        degradation_threshold = thresholds["performance_degradation"]
        
        # Get baseline metrics (from initial deployment or previous period)
        baseline_metrics = self._get_baseline_metrics(model_type, model_version)
        
        for metric_name, current_value in current_metrics.items():
            if metric_name in baseline_metrics:
                baseline_value = baseline_metrics[metric_name]
                
                # Check for degradation (lower is worse for most metrics)
                if metric_name != "error_rate" and metric_name != "latency_ms":
                    degradation = (baseline_value - current_value) / baseline_value
                else:
                    # For error_rate and latency, higher is worse
                    degradation = (current_value - baseline_value) / baseline_value
                
                if degradation > degradation_threshold:
                    self._trigger_alert(
                        alert_type="performance_degradation",
                        severity="high",
                        message=f"{metric_name} degraded by {degradation:.2%} (current: {current_value:.3f}, baseline: {baseline_value:.3f})",
                        model_type=model_type,
                        model_version=model_version
                    )
    
    def _detect_data_drift(self, model_type: ModelType, model_version: str) -> DataDriftResult:
        """Detect data drift in incoming data"""
        
        # In practice, this would compare current data distribution with training data
        # For demonstration, we'll simulate drift detection
        
        # Simulate drift detection using statistical tests
        drift_score = np.random.uniform(0.05, 0.15)
        drift_threshold = self.config["monitoring"]["alert_thresholds"]["data_drift_threshold"]
        
        drift_detected = drift_score > drift_threshold
        affected_features = []
        
        if drift_detected:
            # Simulate which features are affected
            feature_names = ["case_type", "client_location", "case_complexity", "document_length"]
            affected_features = np.random.choice(feature_names, size=np.random.randint(1, 3), replace=False).tolist()
        
        return DataDriftResult(
            drift_detected=drift_detected,
            drift_score=drift_score,
            affected_features=affected_features,
            timestamp=datetime.now()
        )
    
    def _check_bias_increase(self, model_type: ModelType, model_version: str):
        """Check for increases in model bias"""
        
        # Simulate bias monitoring
        current_bias = np.random.uniform(0.02, 0.08)
        baseline_bias = 0.05  # Baseline bias score
        
        bias_increase_threshold = self.config["monitoring"]["alert_thresholds"]["bias_increase_threshold"]
        bias_increase = current_bias - baseline_bias
        
        if bias_increase > bias_increase_threshold:
            self._trigger_alert(
                alert_type="bias_increase",
                severity="high",
                message=f"Model bias increased by {bias_increase:.3f} (current: {current_bias:.3f}, baseline: {baseline_bias:.3f})",
                model_type=model_type,
                model_version=model_version
            )
    
    def _get_baseline_metrics(self, model_type: ModelType, model_version: str) -> Dict[str, float]:
        """Get baseline metrics for comparison"""
        
        # In practice, this would retrieve stored baseline metrics
        # For demonstration, we'll return simulated baseline values
        return {
            "accuracy": 0.87,
            "precision": 0.85,
            "recall": 0.83,
            "f1_score": 0.84,
            "latency_ms": 120,
            "throughput_rps": 110,
            "error_rate": 0.015
        }

    def _store_metrics(self,
                      metrics: Dict[str, float],
                      model_type: ModelType,
                      model_version: str):
        """Store monitoring metrics in database"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        thresholds = self.config["performance_thresholds"]

        for metric_name, value in metrics.items():
            # Determine threshold for this metric
            threshold = thresholds.get(f"min_{metric_name}", 0.0)
            if metric_name in ["error_rate", "latency_ms"]:
                threshold = thresholds.get(f"max_{metric_name}", 1.0)

            # Determine status
            if metric_name in ["error_rate", "latency_ms"]:
                status = "normal" if value <= threshold else "alert"
            else:
                status = "normal" if value >= threshold else "alert"

            cursor.execute('''
                INSERT INTO monitoring_metrics
                (model_type, model_version, metric_name, metric_value, threshold_value, status, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (model_type.value, model_version, metric_name, value, threshold, status, datetime.now()))

        conn.commit()
        conn.close()

    def _log_data_drift(self,
                       drift_result: DataDriftResult,
                       model_type: ModelType,
                       model_version: str):
        """Log data drift detection results"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO data_drift_logs
            (model_type, model_version, drift_detected, drift_score, affected_features, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            model_type.value,
            model_version,
            drift_result.drift_detected,
            drift_result.drift_score,
            json.dumps(drift_result.affected_features),
            drift_result.timestamp
        ))

        conn.commit()
        conn.close()

        if drift_result.drift_detected:
            self._trigger_alert(
                alert_type="data_drift",
                severity="medium",
                message=f"Data drift detected (score: {drift_result.drift_score:.3f}) in features: {', '.join(drift_result.affected_features)}",
                model_type=model_type,
                model_version=model_version
            )

    def _trigger_alert(self,
                      alert_type: str,
                      severity: str,
                      message: str,
                      model_type: ModelType,
                      model_version: str):
        """Trigger an alert and send notifications"""

        # Store alert in database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO alerts
            (alert_type, severity, message, model_type, model_version, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (alert_type, severity, message, model_type.value, model_version, datetime.now()))

        conn.commit()
        conn.close()

        logger.warning(f"ALERT [{severity.upper()}] {alert_type}: {message}")

        # Send notifications
        self._send_notifications(alert_type, severity, message, model_type, model_version)

        # Check if auto-rollback should be triggered
        if severity == "high" and self.config["rollback_policies"]["auto_rollback_enabled"]:
            self._check_auto_rollback(alert_type, model_type, model_version)

    def _send_notifications(self,
                           alert_type: str,
                           severity: str,
                           message: str,
                           model_type: ModelType,
                           model_version: str):
        """Send alert notifications via configured channels"""

        notification_config = self.config["monitoring"]["notification_channels"]

        # Send email notifications
        if "email" in notification_config:
            self._send_email_alert(
                recipients=notification_config["email"],
                alert_type=alert_type,
                severity=severity,
                message=message,
                model_type=model_type,
                model_version=model_version
            )

        # Send Slack notifications
        if "slack_webhook" in notification_config:
            self._send_slack_alert(
                webhook_url=notification_config["slack_webhook"],
                alert_type=alert_type,
                severity=severity,
                message=message,
                model_type=model_type,
                model_version=model_version
            )

    def _send_email_alert(self,
                         recipients: List[str],
                         alert_type: str,
                         severity: str,
                         message: str,
                         model_type: ModelType,
                         model_version: str):
        """Send email alert notification"""

        try:
            # Email configuration (in practice, use environment variables)
            smtp_server = "smtp.gmail.com"
            smtp_port = 587
            sender_email = "<EMAIL>"
            sender_password = "your_app_password"  # Use app password or OAuth

            msg = MimeMultipart()
            msg['From'] = sender_email
            msg['To'] = ", ".join(recipients)
            msg['Subject'] = f"AI Model Alert [{severity.upper()}]: {alert_type}"

            body = f"""
            AI Model Monitoring Alert

            Alert Type: {alert_type}
            Severity: {severity.upper()}
            Model: {model_type.value} v{model_version}
            Message: {message}
            Timestamp: {datetime.now().isoformat()}

            Please review the model performance and take appropriate action.

            Best regards,
            AI Monitoring System
            """

            msg.attach(MimeText(body, 'plain'))

            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(sender_email, sender_password)
            text = msg.as_string()
            server.sendmail(sender_email, recipients, text)
            server.quit()

            logger.info(f"Email alert sent to {recipients}")

        except Exception as e:
            logger.error(f"Failed to send email alert: {str(e)}")

    def _send_slack_alert(self,
                         webhook_url: str,
                         alert_type: str,
                         severity: str,
                         message: str,
                         model_type: ModelType,
                         model_version: str):
        """Send Slack alert notification"""

        try:
            color_map = {
                "low": "#36a64f",      # Green
                "medium": "#ff9500",   # Orange
                "high": "#ff0000"      # Red
            }

            payload = {
                "attachments": [
                    {
                        "color": color_map.get(severity, "#ff0000"),
                        "title": f"AI Model Alert: {alert_type}",
                        "fields": [
                            {
                                "title": "Severity",
                                "value": severity.upper(),
                                "short": True
                            },
                            {
                                "title": "Model",
                                "value": f"{model_type.value} v{model_version}",
                                "short": True
                            },
                            {
                                "title": "Message",
                                "value": message,
                                "short": False
                            },
                            {
                                "title": "Timestamp",
                                "value": datetime.now().isoformat(),
                                "short": True
                            }
                        ]
                    }
                ]
            }

            response = requests.post(webhook_url, json=payload)
            response.raise_for_status()

            logger.info("Slack alert sent successfully")

        except Exception as e:
            logger.error(f"Failed to send Slack alert: {str(e)}")

    def _check_auto_rollback(self,
                           alert_type: str,
                           model_type: ModelType,
                           model_version: str):
        """Check if auto-rollback should be triggered"""

        rollback_triggers = self.config["rollback_policies"]["rollback_triggers"]

        should_rollback = False
        rollback_reason = ""

        if alert_type == "performance_degradation":
            # Check if performance degradation exceeds rollback threshold
            should_rollback = True
            rollback_reason = "Performance degradation exceeded rollback threshold"

        elif alert_type == "bias_increase":
            # Check if bias increase exceeds rollback threshold
            should_rollback = True
            rollback_reason = "Model bias increase exceeded rollback threshold"

        elif alert_type == "error_rate":
            # Check if error rate exceeds rollback threshold
            should_rollback = True
            rollback_reason = "Error rate exceeded rollback threshold"

        if should_rollback:
            self._execute_rollback(model_type, model_version, rollback_reason)

    def _execute_rollback(self,
                         model_type: ModelType,
                         model_version: str,
                         reason: str):
        """Execute model rollback to previous version"""

        try:
            # Get previous stable version
            previous_version = self._get_previous_stable_version(model_type, model_version)

            if not previous_version:
                logger.error(f"No previous stable version found for {model_type.value}")
                return

            logger.warning(f"Initiating rollback from {model_version} to {previous_version}")

            # In practice, this would:
            # 1. Update model serving configuration
            # 2. Switch traffic to previous version
            # 3. Update model registry
            # 4. Notify stakeholders

            # Simulate rollback process
            rollback_success = self._perform_rollback_operation(
                model_type, model_version, previous_version
            )

            if rollback_success:
                # Log successful rollback
                self._log_rollback_event(
                    model_type, model_version, previous_version, reason, "success"
                )

                # Send rollback notification
                self._send_rollback_notification(
                    model_type, model_version, previous_version, reason
                )

                logger.info(f"Rollback completed successfully: {model_version} -> {previous_version}")
            else:
                logger.error(f"Rollback failed for {model_type.value} v{model_version}")

        except Exception as e:
            logger.error(f"Error during rollback execution: {str(e)}")

    def _get_previous_stable_version(self,
                                   model_type: ModelType,
                                   current_version: str) -> Optional[str]:
        """Get the previous stable version for rollback"""

        # In practice, this would query your model registry
        # For demonstration, we'll simulate version history
        version_history = {
            ModelType.DOCUMENT_CLASSIFIER: ["v1.0", "v1.1", "v1.2", "v1.3"],
            ModelType.LEGAL_NER: ["v1.0", "v1.1", "v1.2"],
            ModelType.CASE_PREDICTOR: ["v1.0", "v1.1"],
            ModelType.CONTRACT_ANALYZER: ["v1.0"]
        }

        versions = version_history.get(model_type, [])

        try:
            current_index = versions.index(current_version)
            if current_index > 0:
                return versions[current_index - 1]
        except ValueError:
            pass

        return None

    def _perform_rollback_operation(self,
                                  model_type: ModelType,
                                  current_version: str,
                                  target_version: str) -> bool:
        """Perform the actual rollback operation"""

        try:
            # Simulate rollback steps
            steps = [
                "Validating target version",
                "Updating model serving configuration",
                "Switching traffic routing",
                "Updating model registry",
                "Verifying rollback success"
            ]

            for step in steps:
                logger.info(f"Rollback step: {step}")
                time.sleep(1)  # Simulate processing time

            return True  # Simulate successful rollback

        except Exception as e:
            logger.error(f"Rollback operation failed: {str(e)}")
            return False

    def _log_rollback_event(self,
                          model_type: ModelType,
                          from_version: str,
                          to_version: str,
                          reason: str,
                          status: str):
        """Log rollback event for audit trail"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Create rollback_events table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rollback_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_type TEXT NOT NULL,
                from_version TEXT NOT NULL,
                to_version TEXT NOT NULL,
                reason TEXT NOT NULL,
                status TEXT NOT NULL,
                timestamp DATETIME NOT NULL
            )
        ''')

        cursor.execute('''
            INSERT INTO rollback_events
            (model_type, from_version, to_version, reason, status, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (model_type.value, from_version, to_version, reason, status, datetime.now()))

        conn.commit()
        conn.close()

    def _send_rollback_notification(self,
                                  model_type: ModelType,
                                  from_version: str,
                                  to_version: str,
                                  reason: str):
        """Send notification about rollback event"""

        message = f"Model rollback executed: {model_type.value} v{from_version} -> v{to_version}. Reason: {reason}"

        self._trigger_alert(
            alert_type="rollback_executed",
            severity="high",
            message=message,
            model_type=model_type,
            model_version=from_version
        )

    def get_monitoring_dashboard_data(self,
                                    model_type: ModelType,
                                    model_version: str,
                                    hours_back: int = 24) -> Dict[str, Any]:
        """Get monitoring data for dashboard display"""

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Get recent metrics
        cursor.execute('''
            SELECT metric_name, metric_value, timestamp
            FROM monitoring_metrics
            WHERE model_type = ? AND model_version = ?
            AND timestamp > datetime('now', '-{} hours')
            ORDER BY timestamp DESC
        '''.format(hours_back), (model_type.value, model_version))

        metrics_data = cursor.fetchall()

        # Get recent alerts
        cursor.execute('''
            SELECT alert_type, severity, message, timestamp
            FROM alerts
            WHERE model_type = ? AND model_version = ?
            AND timestamp > datetime('now', '-{} hours')
            ORDER BY timestamp DESC
        '''.format(hours_back), (model_type.value, model_version))

        alerts_data = cursor.fetchall()

        # Get data drift logs
        cursor.execute('''
            SELECT drift_detected, drift_score, affected_features, timestamp
            FROM data_drift_logs
            WHERE model_type = ? AND model_version = ?
            AND timestamp > datetime('now', '-{} hours')
            ORDER BY timestamp DESC
        '''.format(hours_back), (model_type.value, model_version))

        drift_data = cursor.fetchall()

        conn.close()

        return {
            "metrics": metrics_data,
            "alerts": alerts_data,
            "data_drift": drift_data,
            "model_info": {
                "type": model_type.value,
                "version": model_version,
                "monitoring_period_hours": hours_back
            }
        }
