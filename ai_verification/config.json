{"data_quality_thresholds": {"min_data_size": 1000, "max_missing_ratio": 0.05, "min_class_balance": 0.1, "max_duplicate_ratio": 0.02, "min_feature_variance": 0.01}, "performance_thresholds": {"min_accuracy": 0.85, "min_precision": 0.8, "min_recall": 0.8, "min_f1": 0.8, "min_auc_roc": 0.75, "max_bias_score": 0.1, "min_legal_accuracy": 0.9}, "safety_checks": {"enable_bias_detection": true, "enable_privacy_check": true, "enable_legal_compliance": true, "enable_explainability_check": true, "enable_robustness_testing": true}, "model_specific_thresholds": {"document_classifier": {"min_accuracy": 0.88, "min_precision": 0.85, "min_recall": 0.82}, "legal_ner": {"min_accuracy": 0.92, "min_precision": 0.9, "min_recall": 0.88, "min_legal_accuracy": 0.95}, "case_predictor": {"min_accuracy": 0.8, "min_precision": 0.78, "min_recall": 0.75, "max_bias_score": 0.05}, "contract_analyzer": {"min_accuracy": 0.9, "min_precision": 0.88, "min_recall": 0.85, "min_legal_accuracy": 0.93}}, "monitoring": {"enable_continuous_monitoring": true, "monitoring_interval_hours": 24, "alert_thresholds": {"performance_degradation": 0.05, "data_drift_threshold": 0.1, "bias_increase_threshold": 0.02}, "notification_channels": {"email": ["<EMAIL>", "<EMAIL>"], "slack_webhook": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "enable_dashboard_alerts": true}}, "compliance_requirements": {"gdpr_compliance": true, "hipaa_compliance": false, "sox_compliance": true, "legal_ethics_compliance": true, "audit_trail_required": true, "explainability_required": true}, "testing_protocols": {"adversarial_testing": {"enabled": true, "test_cases": ["edge_case_inputs", "malformed_legal_documents", "out_of_domain_cases", "biased_input_scenarios"]}, "stress_testing": {"enabled": true, "max_concurrent_requests": 1000, "test_duration_minutes": 30}, "regression_testing": {"enabled": true, "baseline_model_versions": ["v1.0", "v1.1", "v1.2"], "test_dataset_size": 5000}}, "rollback_policies": {"auto_rollback_enabled": true, "rollback_triggers": {"accuracy_drop_threshold": 0.05, "bias_increase_threshold": 0.03, "error_rate_threshold": 0.1, "latency_increase_threshold": 2.0}, "rollback_notification_required": true, "manual_approval_required": false}, "data_governance": {"data_lineage_tracking": true, "data_versioning": true, "pii_detection": true, "data_retention_days": 2555, "anonymization_required": true}, "model_governance": {"model_versioning": true, "model_registry_enabled": true, "approval_workflow": {"required_approvers": ["data_scientist", "legal_expert", "compliance_officer"], "approval_timeout_hours": 48}, "deployment_gates": {"staging_validation_required": true, "production_canary_deployment": true, "canary_traffic_percentage": 5}}}