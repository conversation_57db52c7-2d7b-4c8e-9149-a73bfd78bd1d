# AI Verification System Requirements
# Core dependencies for the AI Fine-Tuning Verification System

# Data processing and analysis
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.9.0

# Machine learning and model evaluation
scikit-learn>=1.1.0

# Testing framework
pytest>=7.0.0
pytest-mock>=3.8.0

# HTTP requests for notifications
requests>=2.28.0

# Database operations
sqlite3  # Built-in with Python

# Email functionality (built-in)
smtplib  # Built-in with Python
email  # Built-in with Python

# JSON handling (built-in)
json  # Built-in with Python

# Date and time handling (built-in)
datetime  # Built-in with Python

# Threading for monitoring (built-in)
threading  # Built-in with Python

# File operations (built-in)
pathlib  # Built-in with Python
tempfile  # Built-in with Python
os  # Built-in with Python

# Logging (built-in)
logging  # Built-in with Python

# Optional dependencies for enhanced functionality
# Uncomment as needed:

# For advanced ML model support
# tensorflow>=2.10.0
# torch>=1.12.0
# transformers>=4.20.0

# For advanced statistical analysis
# statsmodels>=0.13.0

# For visualization (if dashboard needed)
# matplotlib>=3.5.0
# seaborn>=0.11.0
# plotly>=5.10.0

# For advanced bias detection
# fairlearn>=0.7.0
# aif360>=0.5.0

# For model explainability
# shap>=0.41.0
# lime>=0.2.0

# For advanced data drift detection
# evidently>=0.2.0
# alibi-detect>=0.10.0

# For production deployment
# flask>=2.2.0  # If web dashboard needed
# celery>=5.2.0  # For background tasks
# redis>=4.3.0   # For task queue

# For cloud integration
# boto3>=1.24.0  # AWS integration
# google-cloud-storage>=2.5.0  # GCP integration
# azure-storage-blob>=12.12.0  # Azure integration

# For advanced monitoring
# prometheus-client>=0.14.0
# grafana-api>=1.0.3
