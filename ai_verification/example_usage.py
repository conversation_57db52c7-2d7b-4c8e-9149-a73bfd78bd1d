"""
Example Usage of AI Fine-Tuning Verification System
==================================================

This script demonstrates how to use the AI verification framework
for a legal case management system.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import logging

from verification_framework import AIVerificationFramework, ModelType
from monitoring_system import ContinuousMonitoringSystem

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_sample_legal_data():
    """Create sample legal data for demonstration"""
    
    np.random.seed(42)
    
    # Simulate legal document classification data
    case_types = ['Criminal', 'Civil', 'Family', 'Corporate', 'Immigration']
    document_types = ['Contract', 'Brief', 'Motion', 'Pleading', 'Evidence']
    jurisdictions = ['Federal', 'State', 'Local']
    
    n_samples = 2000
    
    training_data = pd.DataFrame({
        'document_length': np.random.normal(5000, 2000, n_samples),
        'num_citations': np.random.poisson(15, n_samples),
        'case_type': np.random.choice(case_types, n_samples),
        'document_type': np.random.choice(document_types, n_samples),
        'jurisdiction': np.random.choice(jurisdictions, n_samples),
        'complexity_score': np.random.uniform(1, 10, n_samples),
        'urgency_level': np.random.choice([1, 2, 3, 4, 5], n_samples),
        'label': np.random.choice([0, 1], n_samples, p=[0.7, 0.3])  # 0: routine, 1: priority
    })
    
    # Create validation data with slight distribution shift
    validation_data = pd.DataFrame({
        'document_length': np.random.normal(5200, 2100, 400),
        'num_citations': np.random.poisson(16, 400),
        'case_type': np.random.choice(case_types, 400),
        'document_type': np.random.choice(document_types, 400),
        'jurisdiction': np.random.choice(jurisdictions, 400),
        'complexity_score': np.random.uniform(1.2, 9.8, 400),
        'urgency_level': np.random.choice([1, 2, 3, 4, 5], 400),
        'label': np.random.choice([0, 1], 400, p=[0.65, 0.35])
    })
    
    return training_data, validation_data

def demonstrate_verification_pipeline():
    """Demonstrate the complete verification pipeline"""
    
    logger.info("=== AI Fine-Tuning Verification System Demo ===")
    
    # 1. Create sample data
    logger.info("Creating sample legal data...")
    training_data, validation_data = create_sample_legal_data()
    logger.info(f"Training data: {len(training_data)} samples")
    logger.info(f"Validation data: {len(validation_data)} samples")
    
    # 2. Initialize verification framework
    logger.info("Initializing verification framework...")
    verification_framework = AIVerificationFramework()
    
    # 3. Run complete verification pipeline
    logger.info("Running verification pipeline for Legal Document Classifier...")
    
    model_type = ModelType.DOCUMENT_CLASSIFIER
    model_version = "v1.2"
    model_path = "models/legal_document_classifier_v1.2.pkl"  # Simulated path
    
    verification_results = verification_framework.verify_fine_tuning_pipeline(
        model_type=model_type,
        training_data=training_data,
        validation_data=validation_data,
        model_path=model_path,
        model_version=model_version
    )
    
    # 4. Display results
    logger.info("\n=== Verification Results ===")
    for i, result in enumerate(verification_results, 1):
        logger.info(f"\nCheck {i}: {result.message.split()[0]} Verification")
        logger.info(f"Status: {result.status.value.upper()}")
        logger.info(f"Score: {result.score:.3f}")
        logger.info(f"Message: {result.message}")
        
        if result.status.value != "passed":
            logger.warning(f"Issues detected: {result.details.get('issues', [])}")
    
    # 5. Overall assessment
    overall_passed = all(r.status.value == "passed" for r in verification_results)
    overall_score = sum(r.score for r in verification_results) / len(verification_results)
    
    logger.info(f"\n=== Overall Assessment ===")
    logger.info(f"Overall Status: {'PASSED' if overall_passed else 'FAILED'}")
    logger.info(f"Overall Score: {overall_score:.3f}")
    
    if overall_passed:
        logger.info("✅ Model is ready for deployment!")
    else:
        logger.warning("❌ Model requires improvements before deployment")
    
    return verification_results

def demonstrate_continuous_monitoring():
    """Demonstrate continuous monitoring system"""
    
    logger.info("\n=== Continuous Monitoring Demo ===")
    
    # Initialize monitoring system
    monitoring_system = ContinuousMonitoringSystem()
    
    # Simulate monitoring for a deployed model
    model_type = ModelType.LEGAL_NER
    model_version = "v1.1"
    
    logger.info(f"Starting monitoring for {model_type.value} v{model_version}")
    
    # Simulate some monitoring cycles
    for cycle in range(3):
        logger.info(f"\nMonitoring Cycle {cycle + 1}")
        
        # Collect metrics
        metrics = monitoring_system._collect_model_metrics(model_type, model_version)
        logger.info(f"Current metrics: {metrics}")
        
        # Check for performance degradation
        monitoring_system._check_performance_degradation(metrics, model_type, model_version)
        
        # Check for data drift
        drift_result = monitoring_system._detect_data_drift(model_type, model_version)
        logger.info(f"Data drift detected: {drift_result.drift_detected}")
        if drift_result.drift_detected:
            logger.warning(f"Drift score: {drift_result.drift_score:.3f}")
            logger.warning(f"Affected features: {drift_result.affected_features}")
        
        # Store metrics
        monitoring_system._store_metrics(metrics, model_type, model_version)
    
    # Get dashboard data
    dashboard_data = monitoring_system.get_monitoring_dashboard_data(
        model_type, model_version, hours_back=1
    )
    
    logger.info(f"\nDashboard data collected:")
    logger.info(f"- Metrics entries: {len(dashboard_data['metrics'])}")
    logger.info(f"- Alerts: {len(dashboard_data['alerts'])}")
    logger.info(f"- Data drift logs: {len(dashboard_data['data_drift'])}")

def demonstrate_model_specific_verification():
    """Demonstrate verification for different model types"""
    
    logger.info("\n=== Model-Specific Verification Demo ===")
    
    verification_framework = AIVerificationFramework()
    training_data, validation_data = create_sample_legal_data()
    
    model_types = [
        ModelType.DOCUMENT_CLASSIFIER,
        ModelType.LEGAL_NER,
        ModelType.CASE_PREDICTOR,
        ModelType.CONTRACT_ANALYZER
    ]
    
    for model_type in model_types:
        logger.info(f"\nVerifying {model_type.value}...")
        
        # Run verification for each model type
        results = verification_framework.verify_fine_tuning_pipeline(
            model_type=model_type,
            training_data=training_data,
            validation_data=validation_data,
            model_path=f"models/{model_type.value}_v1.0.pkl",
            model_version="v1.0"
        )
        
        # Summary for each model
        passed_checks = sum(1 for r in results if r.status.value == "passed")
        total_checks = len(results)
        avg_score = sum(r.score for r in results) / len(results)
        
        logger.info(f"  Passed: {passed_checks}/{total_checks} checks")
        logger.info(f"  Average score: {avg_score:.3f}")

def demonstrate_compliance_checking():
    """Demonstrate legal compliance checking"""
    
    logger.info("\n=== Legal Compliance Demo ===")
    
    verification_framework = AIVerificationFramework()
    
    # Test different compliance scenarios
    compliance_scenarios = [
        {
            "name": "GDPR Compliant Model",
            "model_type": ModelType.DOCUMENT_CLASSIFIER,
            "privacy_compliant": True,
            "bias_score": 0.03
        },
        {
            "name": "High Bias Model",
            "model_type": ModelType.CASE_PREDICTOR,
            "privacy_compliant": True,
            "bias_score": 0.25  # High bias
        },
        {
            "name": "Privacy Non-Compliant Model",
            "model_type": ModelType.CONTRACT_ANALYZER,
            "privacy_compliant": False,
            "bias_score": 0.05
        }
    ]
    
    for scenario in compliance_scenarios:
        logger.info(f"\nTesting: {scenario['name']}")
        
        # Mock the compliance checks
        with patch.object(verification_framework, '_detect_bias', return_value=scenario['bias_score']):
            with patch.object(verification_framework, '_check_privacy_compliance', return_value=scenario['privacy_compliant']):
                with patch.object(verification_framework, '_check_legal_compliance', return_value=True):
                    
                    result = verification_framework.verify_safety_compliance(
                        "dummy_path",
                        pd.DataFrame(),  # Empty validation data for demo
                        scenario['model_type'],
                        "v1.0"
                    )
                    
                    logger.info(f"  Status: {result.status.value.upper()}")
                    logger.info(f"  Score: {result.score:.3f}")
                    logger.info(f"  Message: {result.message}")

if __name__ == "__main__":
    try:
        # Run all demonstrations
        demonstrate_verification_pipeline()
        demonstrate_continuous_monitoring()
        demonstrate_model_specific_verification()
        
        # Note: The compliance demo requires patching, so it's commented out
        # demonstrate_compliance_checking()
        
        logger.info("\n=== Demo Complete ===")
        logger.info("The AI Fine-Tuning Verification System is ready for use!")
        
    except Exception as e:
        logger.error(f"Demo failed with error: {str(e)}")
        raise
