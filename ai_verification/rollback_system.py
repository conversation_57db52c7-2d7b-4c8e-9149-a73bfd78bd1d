"""
AI Model Rollback and Recovery System
====================================

This module provides automated rollback and recovery mechanisms for AI models
in the legal case management system, ensuring system stability and reliability.
"""

import logging
import json
import sqlite3
import shutil
import os
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import threading
import time

from verification_framework import ModelType, VerificationStatus
from monitoring_system import ContinuousMonitoringSystem

logger = logging.getLogger(__name__)

class RollbackReason(Enum):
    """Reasons for model rollback"""
    PERFORMANCE_DEGRADATION = "performance_degradation"
    BIAS_INCREASE = "bias_increase"
    ERROR_RATE_SPIKE = "error_rate_spike"
    DATA_DRIFT = "data_drift"
    COMPLIANCE_VIOLATION = "compliance_violation"
    MANUAL_TRIGGER = "manual_trigger"
    SECURITY_INCIDENT = "security_incident"

class RollbackStatus(Enum):
    """Status of rollback operations"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class ModelVersion:
    """Container for model version information"""
    version: str
    model_path: str
    deployment_date: datetime
    verification_score: float
    is_stable: bool
    performance_metrics: Dict[str, float]

@dataclass
class RollbackOperation:
    """Container for rollback operation details"""
    id: str
    model_type: ModelType
    from_version: str
    to_version: str
    reason: RollbackReason
    status: RollbackStatus
    initiated_at: datetime
    completed_at: Optional[datetime]
    initiated_by: str  # user or system
    error_message: Optional[str]

class RollbackRecoverySystem:
    """
    Automated rollback and recovery system for AI models
    """
    
    def __init__(self, config_path: str = "ai_verification/config.json"):
        """Initialize the rollback system"""
        self.config = self._load_config(config_path)
        self.db_path = "ai_verification/rollback.db"
        self.model_registry_path = "models/registry"
        self.backup_path = "models/backups"
        self._init_database()
        self._ensure_directories()
        
    def _load_config(self, config_path: str) -> Dict:
        """Load rollback configuration"""
        with open(config_path, 'r') as f:
            return json.load(f)
    
    def _init_database(self):
        """Initialize rollback database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Model versions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS model_versions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_type TEXT NOT NULL,
                version TEXT NOT NULL,
                model_path TEXT NOT NULL,
                deployment_date DATETIME NOT NULL,
                verification_score REAL NOT NULL,
                is_stable BOOLEAN DEFAULT TRUE,
                performance_metrics TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(model_type, version)
            )
        ''')
        
        # Rollback operations table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rollback_operations (
                id TEXT PRIMARY KEY,
                model_type TEXT NOT NULL,
                from_version TEXT NOT NULL,
                to_version TEXT NOT NULL,
                reason TEXT NOT NULL,
                status TEXT NOT NULL,
                initiated_at DATETIME NOT NULL,
                completed_at DATETIME,
                initiated_by TEXT NOT NULL,
                error_message TEXT,
                rollback_details TEXT
            )
        ''')
        
        # Recovery checkpoints table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS recovery_checkpoints (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_type TEXT NOT NULL,
                version TEXT NOT NULL,
                checkpoint_type TEXT NOT NULL,
                checkpoint_data TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def _ensure_directories(self):
        """Ensure required directories exist"""
        Path(self.model_registry_path).mkdir(parents=True, exist_ok=True)
        Path(self.backup_path).mkdir(parents=True, exist_ok=True)
    
    def register_model_version(self, 
                             model_type: ModelType,
                             version: str,
                             model_path: str,
                             verification_score: float,
                             performance_metrics: Dict[str, float]) -> bool:
        """Register a new model version in the system"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO model_versions 
                (model_type, version, model_path, deployment_date, verification_score, performance_metrics)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                model_type.value,
                version,
                model_path,
                datetime.now(),
                verification_score,
                json.dumps(performance_metrics)
            ))
            
            conn.commit()
            conn.close()
            
            # Create backup of the model
            self._create_model_backup(model_type, version, model_path)
            
            logger.info(f"Registered model version: {model_type.value} v{version}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register model version: {str(e)}")
            return False
    
    def _create_model_backup(self, model_type: ModelType, version: str, model_path: str):
        """Create a backup of the model"""
        
        try:
            backup_dir = Path(self.backup_path) / model_type.value / version
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy model files to backup location
            if os.path.isfile(model_path):
                backup_file = backup_dir / Path(model_path).name
                shutil.copy2(model_path, backup_file)
            elif os.path.isdir(model_path):
                backup_dir = backup_dir / "model"
                shutil.copytree(model_path, backup_dir, dirs_exist_ok=True)
            
            logger.info(f"Created backup for {model_type.value} v{version}")
            
        except Exception as e:
            logger.error(f"Failed to create backup: {str(e)}")
    
    def initiate_rollback(self, 
                         model_type: ModelType,
                         current_version: str,
                         reason: RollbackReason,
                         initiated_by: str = "system",
                         target_version: Optional[str] = None) -> str:
        """Initiate a rollback operation"""
        
        try:
            # Get target version if not specified
            if not target_version:
                target_version = self._get_last_stable_version(model_type, current_version)
                
            if not target_version:
                raise ValueError(f"No stable version found for rollback of {model_type.value}")
            
            # Generate rollback operation ID
            rollback_id = f"rb_{model_type.value}_{int(datetime.now().timestamp())}"
            
            # Create rollback operation record
            rollback_op = RollbackOperation(
                id=rollback_id,
                model_type=model_type,
                from_version=current_version,
                to_version=target_version,
                reason=reason,
                status=RollbackStatus.PENDING,
                initiated_at=datetime.now(),
                completed_at=None,
                initiated_by=initiated_by,
                error_message=None
            )
            
            # Store in database
            self._store_rollback_operation(rollback_op)
            
            # Execute rollback in background thread
            rollback_thread = threading.Thread(
                target=self._execute_rollback,
                args=(rollback_op,),
                daemon=True
            )
            rollback_thread.start()
            
            logger.info(f"Initiated rollback {rollback_id}: {current_version} -> {target_version}")
            return rollback_id
            
        except Exception as e:
            logger.error(f"Failed to initiate rollback: {str(e)}")
            raise
    
    def _get_last_stable_version(self, model_type: ModelType, current_version: str) -> Optional[str]:
        """Get the last stable version for rollback"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT version, deployment_date 
            FROM model_versions 
            WHERE model_type = ? AND version != ? AND is_stable = TRUE
            ORDER BY deployment_date DESC
            LIMIT 1
        ''', (model_type.value, current_version))
        
        result = cursor.fetchone()
        conn.close()
        
        return result[0] if result else None
    
    def _store_rollback_operation(self, rollback_op: RollbackOperation):
        """Store rollback operation in database"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO rollback_operations 
            (id, model_type, from_version, to_version, reason, status, initiated_at, initiated_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            rollback_op.id,
            rollback_op.model_type.value,
            rollback_op.from_version,
            rollback_op.to_version,
            rollback_op.reason.value,
            rollback_op.status.value,
            rollback_op.initiated_at,
            rollback_op.initiated_by
        ))
        
        conn.commit()
        conn.close()
    
    def _execute_rollback(self, rollback_op: RollbackOperation):
        """Execute the rollback operation"""
        
        try:
            # Update status to in progress
            self._update_rollback_status(rollback_op.id, RollbackStatus.IN_PROGRESS)
            
            logger.info(f"Executing rollback {rollback_op.id}")
            
            # Step 1: Validate target version
            if not self._validate_target_version(rollback_op.model_type, rollback_op.to_version):
                raise Exception(f"Target version {rollback_op.to_version} is not valid")
            
            # Step 2: Create recovery checkpoint
            self._create_recovery_checkpoint(rollback_op.model_type, rollback_op.from_version)
            
            # Step 3: Stop current model serving
            self._stop_model_serving(rollback_op.model_type, rollback_op.from_version)
            
            # Step 4: Restore target version
            self._restore_model_version(rollback_op.model_type, rollback_op.to_version)
            
            # Step 5: Start serving with target version
            self._start_model_serving(rollback_op.model_type, rollback_op.to_version)
            
            # Step 6: Verify rollback success
            if self._verify_rollback_success(rollback_op.model_type, rollback_op.to_version):
                self._update_rollback_status(rollback_op.id, RollbackStatus.COMPLETED)
                self._send_rollback_notification(rollback_op, success=True)
                logger.info(f"Rollback {rollback_op.id} completed successfully")
            else:
                raise Exception("Rollback verification failed")
                
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Rollback {rollback_op.id} failed: {error_msg}")
            
            # Update status to failed
            self._update_rollback_status(rollback_op.id, RollbackStatus.FAILED, error_msg)
            
            # Attempt recovery
            self._attempt_recovery(rollback_op)
            
            # Send failure notification
            self._send_rollback_notification(rollback_op, success=False, error=error_msg)
    
    def _validate_target_version(self, model_type: ModelType, version: str) -> bool:
        """Validate that the target version exists and is stable"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT is_stable FROM model_versions 
            WHERE model_type = ? AND version = ?
        ''', (model_type.value, version))
        
        result = cursor.fetchone()
        conn.close()
        
        return result and result[0]
    
    def _create_recovery_checkpoint(self, model_type: ModelType, version: str):
        """Create a recovery checkpoint before rollback"""
        
        checkpoint_data = {
            "model_type": model_type.value,
            "version": version,
            "timestamp": datetime.now().isoformat(),
            "serving_config": self._get_current_serving_config(model_type),
            "model_path": self._get_model_path(model_type, version)
        }
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO recovery_checkpoints 
            (model_type, version, checkpoint_type, checkpoint_data)
            VALUES (?, ?, ?, ?)
        ''', (
            model_type.value,
            version,
            "pre_rollback",
            json.dumps(checkpoint_data)
        ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"Created recovery checkpoint for {model_type.value} v{version}")
    
    def _stop_model_serving(self, model_type: ModelType, version: str):
        """Stop the current model from serving requests"""
        
        # In practice, this would:
        # 1. Update load balancer configuration
        # 2. Drain existing connections
        # 3. Stop model serving processes
        # 4. Update service discovery
        
        logger.info(f"Stopping model serving for {model_type.value} v{version}")
        time.sleep(2)  # Simulate stopping time
    
    def _restore_model_version(self, model_type: ModelType, version: str):
        """Restore the target model version from backup"""
        
        try:
            backup_dir = Path(self.backup_path) / model_type.value / version
            
            if not backup_dir.exists():
                raise Exception(f"Backup not found for {model_type.value} v{version}")
            
            # Get target model path
            target_path = self._get_model_path(model_type, version)
            
            # Restore from backup
            if backup_dir.is_dir():
                if Path(target_path).exists():
                    shutil.rmtree(target_path)
                shutil.copytree(backup_dir, target_path)
            
            logger.info(f"Restored model {model_type.value} v{version} from backup")
            
        except Exception as e:
            logger.error(f"Failed to restore model version: {str(e)}")
            raise
    
    def _start_model_serving(self, model_type: ModelType, version: str):
        """Start serving the restored model version"""
        
        # In practice, this would:
        # 1. Start model serving processes
        # 2. Update load balancer configuration
        # 3. Update service discovery
        # 4. Perform health checks
        
        logger.info(f"Starting model serving for {model_type.value} v{version}")
        time.sleep(3)  # Simulate startup time
    
    def _verify_rollback_success(self, model_type: ModelType, version: str) -> bool:
        """Verify that the rollback was successful"""
        
        try:
            # Perform health checks
            health_check = self._perform_health_check(model_type, version)
            
            # Verify model is serving correctly
            serving_check = self._verify_model_serving(model_type, version)
            
            # Check performance metrics
            performance_check = self._check_performance_metrics(model_type, version)
            
            return health_check and serving_check and performance_check
            
        except Exception as e:
            logger.error(f"Rollback verification failed: {str(e)}")
            return False
    
    def _perform_health_check(self, model_type: ModelType, version: str) -> bool:
        """Perform health check on the rolled back model"""
        # Simulate health check
        return True
    
    def _verify_model_serving(self, model_type: ModelType, version: str) -> bool:
        """Verify the model is serving requests correctly"""
        # Simulate serving verification
        return True
    
    def _check_performance_metrics(self, model_type: ModelType, version: str) -> bool:
        """Check that performance metrics are within acceptable ranges"""
        # Simulate performance check
        return True
    
    def _attempt_recovery(self, rollback_op: RollbackOperation):
        """Attempt to recover from a failed rollback"""
        
        try:
            logger.info(f"Attempting recovery for failed rollback {rollback_op.id}")
            
            # Try to restore the original version
            self._restore_model_version(rollback_op.model_type, rollback_op.from_version)
            self._start_model_serving(rollback_op.model_type, rollback_op.from_version)
            
            logger.info(f"Recovery successful for rollback {rollback_op.id}")
            
        except Exception as e:
            logger.error(f"Recovery failed for rollback {rollback_op.id}: {str(e)}")
            # In this case, manual intervention is required
    
    def _update_rollback_status(self, rollback_id: str, status: RollbackStatus, error_message: str = None):
        """Update the status of a rollback operation"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if status == RollbackStatus.COMPLETED:
            cursor.execute('''
                UPDATE rollback_operations 
                SET status = ?, completed_at = ?, error_message = ?
                WHERE id = ?
            ''', (status.value, datetime.now(), error_message, rollback_id))
        else:
            cursor.execute('''
                UPDATE rollback_operations 
                SET status = ?, error_message = ?
                WHERE id = ?
            ''', (status.value, error_message, rollback_id))
        
        conn.commit()
        conn.close()
    
    def _send_rollback_notification(self, rollback_op: RollbackOperation, success: bool, error: str = None):
        """Send notification about rollback completion"""
        
        if success:
            message = f"Rollback completed successfully: {rollback_op.model_type.value} v{rollback_op.from_version} -> v{rollback_op.to_version}"
            severity = "info"
        else:
            message = f"Rollback failed: {rollback_op.model_type.value} v{rollback_op.from_version} -> v{rollback_op.to_version}. Error: {error}"
            severity = "critical"
        
        # Send notification (implementation depends on your notification system)
        logger.info(f"Rollback notification: {message}")
    
    def _get_current_serving_config(self, model_type: ModelType) -> Dict:
        """Get current serving configuration"""
        # Placeholder implementation
        return {"config": "placeholder"}
    
    def _get_model_path(self, model_type: ModelType, version: str) -> str:
        """Get the file path for a model version"""
        return f"models/{model_type.value}/{version}"
    
    def get_rollback_history(self, model_type: Optional[ModelType] = None, limit: int = 50) -> List[Dict]:
        """Get rollback operation history"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if model_type:
            cursor.execute('''
                SELECT * FROM rollback_operations 
                WHERE model_type = ?
                ORDER BY initiated_at DESC 
                LIMIT ?
            ''', (model_type.value, limit))
        else:
            cursor.execute('''
                SELECT * FROM rollback_operations 
                ORDER BY initiated_at DESC 
                LIMIT ?
            ''', (limit,))
        
        columns = [description[0] for description in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        conn.close()
        return results
    
    def get_model_versions(self, model_type: ModelType) -> List[ModelVersion]:
        """Get all versions for a model type"""
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT version, model_path, deployment_date, verification_score, is_stable, performance_metrics
            FROM model_versions 
            WHERE model_type = ?
            ORDER BY deployment_date DESC
        ''', (model_type.value,))
        
        versions = []
        for row in cursor.fetchall():
            versions.append(ModelVersion(
                version=row[0],
                model_path=row[1],
                deployment_date=datetime.fromisoformat(row[2]),
                verification_score=row[3],
                is_stable=bool(row[4]),
                performance_metrics=json.loads(row[5]) if row[5] else {}
            ))
        
        conn.close()
        return versions
