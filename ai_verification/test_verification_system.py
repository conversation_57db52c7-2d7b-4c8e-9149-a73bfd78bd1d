"""
Test Suite for AI Fine-Tuning Verification System
================================================

Comprehensive test suite for validating the AI verification framework,
monitoring system, and all related components.
"""

import pytest
import pandas as pd
import numpy as np
import json
import tempfile
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

from verification_framework import (
    AIVerificationFramework, 
    VerificationStatus, 
    ModelType, 
    VerificationResult,
    ModelMetrics
)
from monitoring_system import ContinuousMonitoringSystem, DataDriftResult

class TestAIVerificationFramework:
    """Test cases for the AI Verification Framework"""
    
    @pytest.fixture
    def sample_training_data(self):
        """Create sample training data for testing"""
        np.random.seed(42)
        data = {
            'feature1': np.random.normal(0, 1, 1000),
            'feature2': np.random.normal(5, 2, 1000),
            'feature3': np.random.choice(['A', 'B', 'C'], 1000),
            'label': np.random.choice([0, 1], 1000, p=[0.6, 0.4])
        }
        return pd.DataFrame(data)
    
    @pytest.fixture
    def sample_validation_data(self):
        """Create sample validation data for testing"""
        np.random.seed(123)
        data = {
            'feature1': np.random.normal(0.1, 1.1, 200),
            'feature2': np.random.normal(5.2, 2.1, 200),
            'feature3': np.random.choice(['A', 'B', 'C'], 200),
            'label': np.random.choice([0, 1], 200, p=[0.65, 0.35])
        }
        return pd.DataFrame(data)
    
    @pytest.fixture
    def verification_framework(self):
        """Create verification framework instance for testing"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config = {
                "data_quality_thresholds": {
                    "min_data_size": 500,
                    "max_missing_ratio": 0.1,
                    "min_class_balance": 0.2,
                    "max_duplicate_ratio": 0.05
                },
                "performance_thresholds": {
                    "min_accuracy": 0.8,
                    "min_precision": 0.75,
                    "min_recall": 0.75,
                    "min_f1": 0.75,
                    "max_bias_score": 0.15
                },
                "safety_checks": {
                    "enable_bias_detection": True,
                    "enable_privacy_check": True,
                    "enable_legal_compliance": True
                }
            }
            json.dump(config, f)
            config_path = f.name
        
        framework = AIVerificationFramework(config_path)
        yield framework
        
        # Cleanup
        os.unlink(config_path)
    
    def test_data_quality_verification_pass(self, verification_framework, sample_training_data, sample_validation_data):
        """Test data quality verification with good data"""
        result = verification_framework.verify_data_quality(sample_training_data, sample_validation_data)
        
        assert isinstance(result, VerificationResult)
        assert result.status == VerificationStatus.PASSED
        assert result.score >= 0.7
        assert "training_size" in result.details
        assert result.details["training_size"] == len(sample_training_data)
    
    def test_data_quality_verification_fail_small_dataset(self, verification_framework, sample_validation_data):
        """Test data quality verification with insufficient data"""
        small_data = sample_validation_data.head(100)  # Too small
        
        result = verification_framework.verify_data_quality(small_data, sample_validation_data)
        
        assert result.status == VerificationStatus.FAILED
        assert result.score < 0.7
        assert "below minimum" in result.message
    
    def test_data_quality_verification_missing_values(self, verification_framework, sample_training_data, sample_validation_data):
        """Test data quality verification with missing values"""
        # Introduce missing values
        data_with_missing = sample_training_data.copy()
        data_with_missing.loc[0:200, 'feature1'] = np.nan
        
        result = verification_framework.verify_data_quality(data_with_missing, sample_validation_data)
        
        assert "Missing data ratio" in result.message or result.status == VerificationStatus.PASSED
        assert "missing_ratio" in result.details
    
    def test_model_performance_verification(self, verification_framework, sample_validation_data):
        """Test model performance verification"""
        with patch.object(verification_framework, '_calculate_model_metrics') as mock_metrics:
            mock_metrics.return_value = ModelMetrics(
                accuracy=0.85,
                precision=0.82,
                recall=0.80,
                f1_score=0.81
            )
            
            result = verification_framework.verify_model_performance(
                "dummy_model_path", 
                sample_validation_data, 
                ModelType.DOCUMENT_CLASSIFIER, 
                "v1.0"
            )
            
            assert isinstance(result, VerificationResult)
            assert result.status == VerificationStatus.PASSED
            assert "metrics" in result.details
    
    def test_model_performance_verification_fail(self, verification_framework, sample_validation_data):
        """Test model performance verification with poor metrics"""
        with patch.object(verification_framework, '_calculate_model_metrics') as mock_metrics:
            mock_metrics.return_value = ModelMetrics(
                accuracy=0.60,  # Below threshold
                precision=0.55,  # Below threshold
                recall=0.50,     # Below threshold
                f1_score=0.52    # Below threshold
            )
            
            result = verification_framework.verify_model_performance(
                "dummy_model_path", 
                sample_validation_data, 
                ModelType.DOCUMENT_CLASSIFIER, 
                "v1.0"
            )
            
            assert result.status == VerificationStatus.FAILED
            assert result.score < 0.7
            assert "below threshold" in result.message
    
    def test_safety_compliance_verification(self, verification_framework, sample_validation_data):
        """Test safety and compliance verification"""
        with patch.object(verification_framework, '_detect_bias', return_value=0.05):
            with patch.object(verification_framework, '_check_privacy_compliance', return_value=True):
                with patch.object(verification_framework, '_check_legal_compliance', return_value=True):
                    
                    result = verification_framework.verify_safety_compliance(
                        "dummy_model_path",
                        sample_validation_data,
                        ModelType.LEGAL_NER,
                        "v1.0"
                    )
                    
                    assert isinstance(result, VerificationResult)
                    assert result.status == VerificationStatus.PASSED
                    assert "bias_score" in result.details
                    assert "privacy_compliant" in result.details
                    assert "legal_compliant" in result.details
    
    def test_safety_compliance_verification_bias_fail(self, verification_framework, sample_validation_data):
        """Test safety verification with high bias"""
        with patch.object(verification_framework, '_detect_bias', return_value=0.25):  # High bias
            with patch.object(verification_framework, '_check_privacy_compliance', return_value=True):
                with patch.object(verification_framework, '_check_legal_compliance', return_value=True):
                    
                    result = verification_framework.verify_safety_compliance(
                        "dummy_model_path",
                        sample_validation_data,
                        ModelType.CASE_PREDICTOR,
                        "v1.0"
                    )
                    
                    assert result.status == VerificationStatus.FAILED
                    assert "bias" in result.message.lower()
    
    def test_legal_domain_verification(self, verification_framework, sample_validation_data):
        """Test legal domain-specific verification"""
        with patch.object(verification_framework, '_calculate_legal_accuracy', return_value=0.92):
            with patch.object(verification_framework, '_perform_legal_domain_checks') as mock_checks:
                mock_checks.return_value = {
                    "citation_accuracy": {"passed": True, "message": "Good", "score": 0.91},
                    "legal_terminology": {"passed": True, "message": "Good", "score": 0.88}
                }
                
                result = verification_framework.verify_legal_domain_accuracy(
                    "dummy_model_path",
                    sample_validation_data,
                    ModelType.LEGAL_NER,
                    "v1.0"
                )
                
                assert isinstance(result, VerificationResult)
                assert result.status == VerificationStatus.PASSED
                assert "legal_accuracy" in result.details
                assert "legal_checks" in result.details
    
    def test_complete_verification_pipeline(self, verification_framework, sample_training_data, sample_validation_data):
        """Test the complete verification pipeline"""
        with patch.object(verification_framework, '_calculate_model_metrics') as mock_metrics:
            with patch.object(verification_framework, '_detect_bias', return_value=0.05):
                with patch.object(verification_framework, '_check_privacy_compliance', return_value=True):
                    with patch.object(verification_framework, '_check_legal_compliance', return_value=True):
                        with patch.object(verification_framework, '_calculate_legal_accuracy', return_value=0.92):
                            with patch.object(verification_framework, '_perform_legal_domain_checks') as mock_checks:
                                
                                mock_metrics.return_value = ModelMetrics(
                                    accuracy=0.87, precision=0.85, recall=0.83, f1_score=0.84
                                )
                                mock_checks.return_value = {
                                    "citation_accuracy": {"passed": True, "message": "Good", "score": 0.91}
                                }
                                
                                results = verification_framework.verify_fine_tuning_pipeline(
                                    ModelType.LEGAL_NER,
                                    sample_training_data,
                                    sample_validation_data,
                                    "dummy_model_path",
                                    "v1.0"
                                )
                                
                                assert len(results) == 4  # Four verification steps
                                assert all(isinstance(r, VerificationResult) for r in results)
                                assert len(verification_framework.verification_history) >= 4


class TestContinuousMonitoringSystem:
    """Test cases for the Continuous Monitoring System"""
    
    @pytest.fixture
    def monitoring_system(self):
        """Create monitoring system instance for testing"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config = {
                "monitoring": {
                    "monitoring_interval_hours": 1,
                    "alert_thresholds": {
                        "performance_degradation": 0.05,
                        "data_drift_threshold": 0.1,
                        "bias_increase_threshold": 0.02
                    },
                    "notification_channels": {
                        "email": ["<EMAIL>"],
                        "enable_dashboard_alerts": True
                    }
                },
                "performance_thresholds": {
                    "min_accuracy": 0.8,
                    "min_precision": 0.75,
                    "max_error_rate": 0.05
                },
                "rollback_policies": {
                    "auto_rollback_enabled": True,
                    "rollback_triggers": {
                        "accuracy_drop_threshold": 0.05,
                        "bias_increase_threshold": 0.03
                    }
                }
            }
            json.dump(config, f)
            config_path = f.name
        
        # Use temporary database
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as db_file:
            db_path = db_file.name
        
        system = ContinuousMonitoringSystem(config_path)
        system.db_path = db_path
        system._init_database()
        
        yield system
        
        # Cleanup
        os.unlink(config_path)
        os.unlink(db_path)
    
    def test_monitoring_system_initialization(self, monitoring_system):
        """Test monitoring system initialization"""
        assert monitoring_system.config is not None
        assert monitoring_system.monitoring_active == False
        assert os.path.exists(monitoring_system.db_path)
    
    def test_data_drift_detection(self, monitoring_system):
        """Test data drift detection"""
        drift_result = monitoring_system._detect_data_drift(ModelType.DOCUMENT_CLASSIFIER, "v1.0")
        
        assert isinstance(drift_result, DataDriftResult)
        assert isinstance(drift_result.drift_detected, bool)
        assert isinstance(drift_result.drift_score, float)
        assert isinstance(drift_result.affected_features, list)
        assert isinstance(drift_result.timestamp, datetime)
    
    def test_metric_collection(self, monitoring_system):
        """Test model metrics collection"""
        metrics = monitoring_system._collect_model_metrics(ModelType.LEGAL_NER, "v1.0")
        
        assert isinstance(metrics, dict)
        assert "accuracy" in metrics
        assert "precision" in metrics
        assert "recall" in metrics
        assert all(0 <= v <= 1 for k, v in metrics.items() if k in ["accuracy", "precision", "recall"])
    
    def test_alert_triggering(self, monitoring_system):
        """Test alert triggering mechanism"""
        with patch.object(monitoring_system, '_send_notifications') as mock_notify:
            monitoring_system._trigger_alert(
                alert_type="test_alert",
                severity="high",
                message="Test alert message",
                model_type=ModelType.CASE_PREDICTOR,
                model_version="v1.0"
            )
            
            mock_notify.assert_called_once()
    
    def test_rollback_execution(self, monitoring_system):
        """Test rollback execution"""
        with patch.object(monitoring_system, '_get_previous_stable_version', return_value="v0.9"):
            with patch.object(monitoring_system, '_perform_rollback_operation', return_value=True):
                with patch.object(monitoring_system, '_send_rollback_notification'):
                    
                    monitoring_system._execute_rollback(
                        ModelType.DOCUMENT_CLASSIFIER,
                        "v1.0",
                        "Test rollback"
                    )
                    
                    # Verify rollback was logged (would check database in real implementation)
                    assert True  # Placeholder assertion
    
    def test_dashboard_data_retrieval(self, monitoring_system):
        """Test dashboard data retrieval"""
        # First store some test data
        test_metrics = {"accuracy": 0.85, "precision": 0.82}
        monitoring_system._store_metrics(test_metrics, ModelType.LEGAL_NER, "v1.0")
        
        dashboard_data = monitoring_system.get_monitoring_dashboard_data(
            ModelType.LEGAL_NER, "v1.0", hours_back=24
        )
        
        assert isinstance(dashboard_data, dict)
        assert "metrics" in dashboard_data
        assert "alerts" in dashboard_data
        assert "data_drift" in dashboard_data
        assert "model_info" in dashboard_data


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
