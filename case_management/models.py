import re
from case_management.extensions import db
from datetime import datetime, timezone
from sqlalchemy.orm import validates
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import CheckConstraint
from sqlalchemy.dialects.mysql import BIGINT as MySQL_BIGINT


class UserMixin:
    # Password hashing
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class LawFirm(db.Model):
    __tablename__ = 'law_firm'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    address = db.Column(db.Text, nullable=True)
    contact_email = db.Column(db.String(255), nullable=True)
    phone_number = db.Column(db.String(20), nullable=True)

    # Relationships
    attorneys = db.relationship('Attorney', backref='law_firm', lazy=True)
    employees = db.relationship('Employee', backref='law_firm', lazy=True)
    clients = db.relationship('Client', back_populates='law_firm', lazy=True)
    
attorney_client_collaboration = db.Table(
    'attorney_client_collaboration',
    db.Column('client_id', db.String(20), db.ForeignKey('client.client_id'), primary_key=True),
    db.Column('attorney_id', db.String(50), db.ForeignKey('attorney.attorney_id'), primary_key=True)
)


class Attorney(db.Model, UserMixin):
    __tablename__ = 'attorney'
    id = db.Column(db.Integer, primary_key=True)
    attorney_id = db.Column(db.String(50), unique=True, nullable=True)
    name = db.Column(db.String(100), nullable=False)
    specialization = db.Column(db.String(100), nullable=True)
    description = db.Column(db.Text, nullable=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password_hash = db.Column(db.String(512), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    phone_number = db.Column(db.String(15), nullable=True)
    address = db.Column(db.String(255), nullable=True)
    role = db.Column(db.String(50), nullable=False)  # e.g., "Partner", "Founder"
    hourly_rate = db.Column(db.Numeric(10, 2), nullable=False)
    law_firm_id = db.Column(db.Integer, db.ForeignKey('law_firm.id'), nullable=False)
    last_login = db.Column(db.DateTime, nullable=True)
    date_added = db.Column(db.DateTime, server_default=db.func.current_timestamp())
    is_hourly = db.Column(db.Boolean, nullable=False, default=True)

    # Relationship to ActivityLog
    activities = db.relationship('ActivityLog', backref='attorney', lazy=True)
    
# Many-to-Many Relationship with Clients (Collaborating Clients)
    collaborating_clients = db.relationship(
        'Client',
        secondary='attorney_client_collaboration',
        back_populates='collaborating_attorneys',
        lazy=True
    )

    # One-to-Many Relationship with Clients (Primary Clients)
    primary_clients = db.relationship(
        'Client',
        foreign_keys='Client.primary_attorney_id',
        back_populates='primary_attorney',
        lazy=True
    )
        
    @validates('email')
    def validate_email(self, key, email):
        """Ensure email follows a valid pattern."""
        email_regex = r'^[\w\.-]+@[\w\.-]+\.\w+$'
        if not re.match(email_regex, email):
            raise ValueError("Invalid email format")
        return email
    
     # Validate phone number format
    @validates('phone_number')
    def validate_phone_number(self, key, phone_number):
        if phone_number is not None:
            # Regex for U.S. phone numbers (supports formats like (*************, ************, etc.)
            phone_regex = r'^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$'
            if not re.match(phone_regex, phone_number):
                raise ValueError('Invalid U.S. phone number format. Expected formats: (*************, ************, etc.')
        return phone_number

     # Validate address format (street number, street name, city, state, and zip code)
    @validates('address')
    def validate_address(self, key, address):
        if address is not None:
            # Regex to validate street number, street name, city, state, and zip code
            address_regex = r'^\d+\s+[\w\s\.]+,\s*[\w\s\.]+,\s*[A-Za-z]{2},\s*\d{5}(-\d{4})?$'
            if not re.match(address_regex, address):
                raise ValueError(
                    'Invalid address format. Expected format: Street number and name, City, State, ZIP (e.g., "123 Main St, Springfield, IL, 62704").'
                )
        return address


    def __repr__(self):
        return f'<Attorney {self.name}>'
  # Password hashing
    def set_password(self, password):
        from werkzeug.security import generate_password_hash
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        from werkzeug.security import check_password_hash
        return check_password_hash(self.password_hash, password)
    
class Employee(db.Model, UserMixin):
    __tablename__ = 'employee'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(255), unique=True, nullable=False)
    role = db.Column(db.String(50), nullable=False)
    hourly_rate = db.Column(db.Numeric(10, 2), nullable=False)
    law_firm_id = db.Column(db.Integer, db.ForeignKey('law_firm.id'), nullable=False)
    username = db.Column(db.String(255), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    address = db.Column(db.Text, nullable=True)
    phone_number = db.Column(db.String(20), nullable=True)  # New field for phone number
    
    last_login = db.Column(db.DateTime, nullable=True)
    date_added = db.Column(db.DateTime, server_default=db.func.current_timestamp())
    is_hourly = db.Column(db.Boolean, nullable=False, default=True)


    # Relationship to ActivityLog
    activities = db.relationship('ActivityLog', backref='employee', lazy=True)

class Client(db.Model):
    __tablename__ = 'client'
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.String(50), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password_hash = db.Column(db.String(512), nullable=False)
    description = db.Column(db.Text, nullable=True)
    email = db.Column(db.String(100), unique=True, nullable=False)
    phone_number = db.Column(db.String(15), nullable=True)
    address = db.Column(db.String(255), nullable=True)
    custom_fields = db.Column(db.JSON)
    law_firm_id = db.Column(db.Integer, db.ForeignKey('law_firm.id'), nullable=False)
    
      # Relationship to LawFirm
    law_firm = db.relationship('LawFirm', back_populates='clients', lazy=True)
    
    # Primary Attorney (One-to-Many Relationship)
    primary_attorney_id = db.Column(db.String(50), db.ForeignKey('attorney.attorney_id'), nullable=True)
    primary_attorney = db.relationship('Attorney', foreign_keys=[primary_attorney_id], back_populates='primary_clients', lazy=True)

    # Collaborating Attorneys (Many-to-Many Relationship)
    collaborating_attorneys = db.relationship(
        'Attorney',
        secondary='attorney_client_collaboration',
        back_populates='collaborating_clients',
        lazy=True
    )

    # Relationship to Case
    cases = db.relationship('Case', back_populates='client')

    
    
    @validates('email')
    def validate_email(self, key, email):
        """Ensure email follows a valid pattern."""
        email_regex = r'^[\w\.-]+@[\w\.-]+\.\w+$'
        if not re.match(email_regex, email):
            raise ValueError("Invalid email format")
        return email
    
    # Validate phone number format
    @validates('phone_number')
    def validate_phone_number(self, key, phone_number):
        if phone_number is not None:
            # Regex for U.S. phone numbers (supports formats like (*************, ************, etc.)
            phone_regex = r'^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$'
            if not re.match(phone_regex, phone_number):
                raise ValueError('Invalid U.S. phone number format. Expected formats: (*************, ************, etc.')
        return phone_number

     # Validate address format (street number, street name, city, state, and zip code)
    @validates('address')
    def validate_address(self, key, address):
        if address is not None:
            # Regex to validate street number, street name, city, state, and zip code
            address_regex = r'^\d+\s+[\w\s\.]+,\s*[\w\s\.]+,\s*[A-Za-z]{2},\s*\d{5}(-\d{4})?$'
            if not re.match(address_regex, address):
                raise ValueError(
                    'Invalid address format. Expected format: Street number and name, City, State, ZIP (e.g., "123 Main St, Springfield, IL, 62704").'
                )
        return address

    def __repr__(self):
        return f'<Client {self.name}>'
    
    # Password hashing
    def set_password(self, password):
        from werkzeug.security import generate_password_hash
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        from werkzeug.security import check_password_hash
        return check_password_hash(self.password_hash, password)

class Admin(db.Model):
    __tablename__ = 'admin'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password_hash = db.Column(db.String(512), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    phone_number = db.Column(db.String(15), nullable=True)
    last_login = db.Column(db.DateTime, nullable=True)
    date_added = db.Column(db.DateTime, server_default=db.func.current_timestamp())

    def set_password(self, password):
        from werkzeug.security import generate_password_hash
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        from werkzeug.security import check_password_hash
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f"<Admin {self.username}>"

 
class ActivityLog(db.Model):
    __tablename__ = 'activity_log'

    id = db.Column(db.Integer, primary_key=True)
    case_id = db.Column(db.String(50), db.ForeignKey('case.case_id'), nullable=False)
    invoice_id = db.Column(
            db.BigInteger()
                .with_variant(db.Integer, "sqlite")
                .with_variant(MySQL_BIGINT(unsigned=True), "mysql"),
            db.ForeignKey('invoices.invoice_id'),
            nullable=True
        )
    activity_type = db.Column(db.String(255), nullable=False)  # e.g., "Court Appearance"
    time_spent = db.Column(db.Numeric(5, 2), nullable=False)  # Time in hours
    description = db.Column(db.Text, nullable=True)
    total_amount = db.Column(db.Numeric(10, 2), nullable=False)  # Calculated amount

    # Relationships: Either an Attorney or Employee logs the activity
    attorney_id = db.Column(db.String(50), db.ForeignKey('attorney.attorney_id'), nullable=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=True)

        # Relación con invoice
    invoice = db.relationship(
        "Invoice", 
        back_populates="activity_logs", 
        lazy=True,
        passive_deletes=True  # Restrict delete to prevent orphaned expenses
    )

    timestamp = db.Column(db.TIMESTAMP, default=db.func.current_timestamp())

    def __repr__(self):
        return f"<ActivityLog {self.id} - Invoice {self.invoice_id}>"

class Case(db.Model):
    __tablename__ = 'case'

    id = db.Column(db.Integer, primary_key=True)
    case_id = db.Column(db.String(50), unique=True, nullable=False)
    client_id = db.Column(db.String(50), db.ForeignKey('client.client_id', ondelete='CASCADE'), nullable=False)
    client_name = db.Column(db.String(100), nullable=False)
    case_name = db.Column(db.String(100), nullable=False)
    case_type = db.Column(db.String(50), nullable=False)
    case_status = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text, nullable=True)
    custom_fields = db.Column(db.JSON)

    attorney_id = db.Column(
        db.String(50),
        db.ForeignKey('attorney.attorney_id', ondelete='SET NULL'),
        nullable=True
    )
    employee_id = db.Column(
        db.Integer,
        db.ForeignKey('employee.id', ondelete='SET NULL'),
        nullable=True
    )
    
    billing_info_id = db.Column(db.Integer, db.ForeignKey('billing_info.id'), nullable=True)

    billing_info = db.relationship('BillingInfo', backref='cases', lazy=True)

    # Relationships
    client = db.relationship('Client', back_populates='cases')
    attorney = db.relationship('Attorney', backref='assigned_cases', foreign_keys=[attorney_id])
    employee = db.relationship('Employee', backref='assigned_cases', foreign_keys=[employee_id])

    deadlines = db.relationship('CaseDeadline', back_populates='case', cascade='all, delete-orphan')
    tasks = db.relationship('CaseTask', back_populates='case', cascade='all, delete-orphan')

    expenses = db.relationship(
        "Expense",
        back_populates="case",
        lazy=True
        # Don't use cascade here to prevent deleting expenses when a case is deleted
    )
    time_entries = db.relationship(
        "TimeEntry",
        back_populates="case",
        cascade='all, delete-orphan'
    )

    def __repr__(self):
        return f'<Case {self.case_id}>'
    
class CaseDeadline(db.Model):
    __tablename__ = 'case_deadline'
    id = db.Column(db.Integer, primary_key=True)
    case_id = db.Column(db.String(50), db.ForeignKey('case.case_id', ondelete='CASCADE'), nullable=False)
    deadline_date = db.Column(db.DateTime, nullable=False)  # The actual deadline date/time
    deadline_type = db.Column(db.String(50), nullable=False)  # e.g., "motion", "hearing", "trial", "evidence production"
    notes = db.Column(db.Text, nullable=True)  # Optional details about the deadline

    # Relationship back to Case
    case = db.relationship('Case', back_populates='deadlines')

    def __repr__(self):
        return f"<CaseDeadline {self.deadline_type} on {self.deadline_date}>"

class CaseTask(db.Model):
    __tablename__ = 'case_task'
    
    id = db.Column(db.Integer, primary_key=True)
    case_id = db.Column(db.String(50), db.ForeignKey('case.case_id', ondelete='CASCADE'), nullable=False)
    task_type = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text, nullable=True)
    due_date = db.Column(db.DateTime, nullable=True)
    status = db.Column(db.String(50), nullable=False, default='pending')
    priority = db.Column(db.String(20), nullable=False, default='medium')  # low, medium, high
    
    # Only keep employee and client assignment options
    assigned_to_employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=True)
    assigned_to_client_id = db.Column(db.String(50), db.ForeignKey('client.client_id'), nullable=True)
    
    # Relationships (simplified)
    case = db.relationship('Case', back_populates='tasks')
    assigned_employee = db.relationship('Employee', backref='assigned_tasks')
    assigned_client = db.relationship('Client', backref='assigned_tasks')

    def __repr__(self):
        return f"<CaseTask {self.id}: {self.task_type} (Case {self.case_id})>"
    
class Document(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)  # Stored filename
    original_name = db.Column(db.String(255), nullable=False)  # Original filename
    case_id = db.Column(db.String(50), db.ForeignKey('case.case_id'), nullable=False)  # Link to case
    client_id = db.Column(db.String(50), db.ForeignKey('client.client_id'), nullable=False)  # Link to client
    uploaded_at = db.Column(db.DateTime, default=db.func.current_timestamp())  # Timestamp of upload
    document_type = db.Column(db.String(100), nullable=False)  # Predefined type or "Custom"
    custom_document_type_id = db.Column(db.Integer, db.ForeignKey('custom_document_type.id'), nullable=True)  # Link to custom type
    uploaded_by = db.Column(db.String(50), nullable=False)  # Who uploaded the document (e.g., attorney_id or client_id)
    ocr_text = db.Column(db.Text, nullable=True)  # extracted OCR content
    title = db.Column(db.String(255), nullable=True)
    author_name = db.Column(db.String(255), nullable=True)
    author_organization = db.Column(db.String(255), nullable=True)
    document_date = db.Column(db.Date, nullable=True)
    intended_recipient = db.Column(db.String(255), nullable=True)
    jurisdiction = db.Column(db.String(255), nullable=True)
    issue_linkage = db.Column(db.String(255), nullable=True)
    access_permissions = db.Column(db.Enum('Internal', 'Client-Viewable', 'Privileged'), default='Internal')
    completeness_flag = db.Column(db.Enum('Complete', 'Partial'), default='Complete')
    # Relationship to CustomDocumentType
    custom_document_type = db.relationship('CustomDocumentType', back_populates='documents')
    versions = db.relationship('DocumentVersion', back_populates='document', cascade="all, delete-orphan")


    def __repr__(self):
        return f'<Document {self.original_name} for Case {self.case_id}>'
    

class CustomDocumentType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    type_name = db.Column(db.String(100), nullable=False, unique=True)  # Name of the custom type
    created_by = db.Column(db.String(100), nullable=False)  # Format: "role:id" (e.g., "attorney:attorney1")
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())

    # Relationship to Document
    documents = db.relationship('Document', back_populates='custom_document_type')

    def __repr__(self):
        return f'<CustomDocumentType {self.type_name}>'
    
class DocumentVersion(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    document_id = db.Column(db.Integer, db.ForeignKey('document.id'), nullable=False)  # Link to parent document
    filename = db.Column(db.String(255), nullable=False)  # Unique filename for this version
    version_number = db.Column(db.Integer, nullable=False)  # Version number (e.g., 1, 2, 3)
    uploaded_by = db.Column(db.String(50), nullable=False)  # Who uploaded this version
    uploaded_at = db.Column(db.DateTime, default=db.func.current_timestamp())  # Timestamp of upload

    # Relationship to Document
    document = db.relationship('Document', back_populates='versions')

    def __repr__(self):
        return f'<DocumentVersion {self.filename} (v{self.version_number})>'
    
class Message(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    case_id = db.Column(db.String(50), nullable=False)
    sender = db.Column(db.String(50), nullable=False)
    content = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
class Expense(db.Model):
    __tablename__ = 'expenses'

    expense_id = db.Column(
        db.BigInteger()
            .with_variant(db.Integer, "sqlite")
            .with_variant(MySQL_BIGINT(unsigned=True), "mysql"),
        primary_key=True
    )
    invoice_id = db.Column(
        db.BigInteger()
            .with_variant(db.Integer, "sqlite")
            .with_variant(MySQL_BIGINT(unsigned=True), "mysql"),
        db.ForeignKey("invoices.invoice_id", ondelete="SET NULL"), 
        nullable=True
    )

    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=True)
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    incurred_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    case_id = db.Column(
        db.String(50),
        db.ForeignKey("case.case_id", ondelete="SET NULL"),
        nullable=True  # It could be null if not linked to a case
    )

    case = db.relationship(
        "Case",
        back_populates="expenses",
        lazy=True,
        passive_deletes=True  # Restrict delete to prevent orphaned expenses
    )

    #Relationship with case and invoice_id nullable to prevent to lose the bills

    # Relación con invoice
    invoice = db.relationship(
        "Invoice", 
        back_populates="expenses", 
        lazy=True,
        passive_deletes=True  # Restrict delete to prevent orphaned expenses
    )
    time_entries = db.relationship(  # Add this relationship
        "TimeEntry",
        back_populates="expense",
        lazy=True,
        passive_deletes=True
    )

    def __repr__(self):
        return f"<Expense {self.expense_id} - Invoice {self.invoice_id}>"


class Invoice(db.Model):
    __tablename__ = 'invoices'

    invoice_id = db.Column(
        db.BigInteger()
            .with_variant(db.Integer, "sqlite")
            .with_variant(MySQL_BIGINT(unsigned=True), "mysql"),
        primary_key=True
    )
    case_id = db.Column(db.String(20), db.ForeignKey("case.case_id"), nullable=False)
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    description = db.Column(db.Text, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default="unpaid")
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=None, onupdate=lambda: datetime.now(timezone.utc))
    created_by_id = db.Column(db.String(50), default=None)
    created_by_type = db.Column(db.String(50), default=None)  # 'attorney' or 'employee'
    shared = db.Column(db.Boolean, default=False)
    filename = db.Column(db.String(255), nullable = True)

    # Foreign key to the Case table
    case = db.relationship('Case', backref='invoices', lazy=True)

    # Inverse relationship to Expense
    # Relación a Expense
    expenses = db.relationship(
        "Expense",
        back_populates="invoice",
        lazy=True,
        passive_deletes=True
    )

    # Relación a ActivityLog
    activity_logs = db.relationship(
        "ActivityLog",
        back_populates="invoice",
        lazy=True,
        passive_deletes=True 
    )

    time_entries = db.relationship(
        "TimeEntry",
        backref="invoice",  # Using backref instead of back_populates
        lazy=True,
        passive_deletes=True
    )
    
    def __repr__(self):
        return f"<Invoice {self.invoice_id}>"
    
class EmployeePermission(db.Model):
    __tablename__ = "employee_permissions"

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey("employee.id", ondelete="CASCADE"), nullable=False)
    permission_name = db.Column(db.String(100), nullable=False)

    def __repr__(self):
        return f"<EmployeePermission employee_id={self.employee_id} permission_name='{self.permission_name}'>"
        
    
class ExternalContact(db.Model):
      __tablename__ = 'external_contact'
      id = db.Column(db.Integer, primary_key=True)
      name = db.Column(db.String(255), nullable=False)
      role = db.Column(db.String(50))  # e.g. "Judge", "Expert"
      last_login = db.Column(db.DateTime, nullable=True)  # Usually None
      date_added = db.Column(db.DateTime, server_default=db.func.current_timestamp())

class EmployeeWorkPeriod(db.Model):
    __tablename__ = 'EmployeeWorkPeriod'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id', ondelete='CASCADE'), nullable=False)
    period_start = db.Column(db.Date, nullable=False)
    period_end = db.Column(db.Date, nullable=False)
    hours_worked = db.Column(db.Numeric(6, 2), nullable=False)

    employee = db.relationship('Employee', backref=db.backref('work_periods', lazy=True))


class AttorneyWorkPeriod(db.Model):
    __tablename__ = 'AttorneyWorkPeriod'

    id = db.Column(db.Integer, primary_key=True)
    attorney_id = db.Column(db.String(50), db.ForeignKey('attorney.attorney_id', ondelete='CASCADE'), nullable=False)
    period_start = db.Column(db.Date, nullable=False)
    period_end = db.Column(db.Date, nullable=False)
    hours_worked = db.Column(db.Numeric(6, 2), nullable=False)

    attorney = db.relationship('Attorney', backref=db.backref('work_periods', lazy=True))

class PayrollAdjustment(db.Model):
    __tablename__ = 'PayrollAdjustment'

    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id', ondelete='CASCADE'), nullable=False)
    period_start = db.Column(db.Date, nullable=False)
    period_end = db.Column(db.Date, nullable=False)
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    description = db.Column(db.Text)
    is_bonus = db.Column(db.Boolean, default=True)

    employee = db.relationship('Employee', backref=db.backref('payroll_adjustments', lazy=True))

# BillingInfo model and accounting trus-processing
class BillingInfo(db.Model):
    __tablename__ = 'billing_info'

    id = db.Column(db.Integer, primary_key=True)
    billing_method = db.Column(db.String(50), nullable=False)  # 'credit_card', 'ach', 'bitcoin', etc.
    encrypted_details = db.Column(db.Text, nullable=True)  # e.g., JSON string with encrypted info
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<BillingInfo(method={self.billing_method})>"
    
class CheckoutSession(db.Model):
    __tablename__ = 'checkout_session'

    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.String(255), nullable=False, unique=True)
    invoice_id = db.Column(
        db.BigInteger()
            .with_variant(db.Integer, "sqlite")
            .with_variant(MySQL_BIGINT(unsigned=True), "mysql"),
        db.ForeignKey('invoices.invoice_id'),
        nullable=False,
        unique=True
    )
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

class TrustDeposit(db.Model):
    __tablename__ = 'TrustDeposit'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.String(50), db.ForeignKey('client.client_id'))
    case_id = db.Column(db.String(50), db.ForeignKey('case.case_id'))
    amount = db.Column(db.Float)
    deposit_date = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

class Transfer(db.Model):
    __tablename__ = 'Transfer'

    id = db.Column(db.Integer, primary_key=True)
    case_id = db.Column(db.String(50), db.ForeignKey('case.case_id'))
    amount = db.Column(db.Float)
    transfer_date = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    activity_id = db.Column(db.Integer, db.ForeignKey('activity_log.id'), nullable=True)
    expense_id = db.Column(
        db.BigInteger()
            .with_variant(db.Integer, "sqlite")
            .with_variant(MySQL_BIGINT(unsigned=True), "mysql"),
        db.ForeignKey('expenses.expense_id'),
        nullable=True
    )
class CaseContactAssignment(db.Model):
    __tablename__ = 'case_contact_assignment'

    id = db.Column(db.Integer, primary_key=True)
    case_id = db.Column(db.String(50), db.ForeignKey('case.case_id'), nullable=False)
    external_contact_id = db.Column(db.Integer, db.ForeignKey('external_contact.id'), nullable=False)
    role = db.Column(db.String(100), nullable=False)  # e.g., Witness, Interpreter
    assigned_date = db.Column(db.DateTime, default=db.func.current_timestamp())

    # Relationships (optional, but useful for backrefs)
    case = db.relationship('Case', backref='external_assignments')
    external_contact = db.relationship('ExternalContact', backref='case_assignments')

    __table_args__ = (
        db.UniqueConstraint('case_id', 'external_contact_id', name='uq_case_contact_pair'),
    )



class SignatureRequest(db.Model):
    __tablename__ = 'signature_requests'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    case_id = db.Column(db.String(50), db.ForeignKey('case.case_id'), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    status = db.Column(db.String(50), default='pending')  # 'pending' or 'signed'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Lead(db.Model):
    __tablename__ = 'lead'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(255), nullable=False)
    phone = db.Column(db.String(20), nullable=True)
    message = db.Column(db.Text, nullable=True)
    source_url = db.Column(db.String(512), nullable=True)
    status = db.Column(db.String(50), default='new')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    attorney_id = db.Column(db.String(50), db.ForeignKey('attorney.attorney_id'), nullable=False)

    def __repr__(self):
        return f"<Lead {self.name} ({self.email})>"
    
############################################    
# nofications view model to store notifications
############################################

class Notification(db.Model):
    __tablename__ = 'notifications'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(50), nullable=False)  # Can be attorney_id or client_id
    user_type = db.Column(db.String(20), nullable=False)  # 'attorney' or 'client'
    message = db.Column(db.String(255), nullable=False)
    notification_type = db.Column(db.String(50), nullable=False)  # 'deadline', 'message', 'task', 'document'
    related_id = db.Column(db.String(50))  # ID of related item (case_id, task_id, etc.)
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<Notification {self.id} for {self.user_type} {self.user_id}>'


class RolePermission(db.Model):
    __tablename__ = 'role_permission'

    id = db.Column(db.Integer, primary_key=True)
    role = db.Column(db.String(50), nullable=False)
    action = db.Column(db.String(100), nullable=False)
    allowed = db.Column(db.Boolean, default=True)

    __table_args__ = (
        db.UniqueConstraint('role', 'action', name='uq_role_action'),
    )

    def __repr__(self):
        return f"<RolePermission {self.role} - {self.action}: {self.allowed}>"

class TimeEntry(db.Model):
    __tablename__ = 'time_entries'
    
    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.Date, nullable=False)
    case_id = db.Column(db.String(50), db.ForeignKey('case.case_id'), nullable=False)
    description = db.Column(db.Text, nullable=False)
    duration = db.Column(db.Numeric(5, 2), nullable=False)
    created_by_id = db.Column(db.Integer, nullable=False)  # Stores user ID from session
    created_by_type = db.Column(db.String(20), nullable=False)  # 'attorney', 'employee', etc.
    updated_by_id = db.Column(db.Integer)  # Stores user ID from session for updates
    updated_by_type = db.Column(db.String(20))  # 'attorney', 'employee', etc.
    
    # Separate FKs for Attorney/Employee
    billed_by_attorney_id = db.Column(
        db.Integer, 
        db.ForeignKey('attorney.id'), 
        nullable=True
    )
    billed_by_employee_id = db.Column(
        db.Integer, 
        db.ForeignKey('employee.id'), 
        nullable=True
    )
    
    billing_rate = db.Column(db.Numeric(10, 2), nullable=False)
    is_billable = db.Column(db.Boolean, default=True)
    tags = db.Column(db.JSON, nullable=True)
    sync_status = db.Column(db.String(20), default="unsynced")
    entry_method = db.Column(db.String(10), nullable=False)
    expense_id = db.Column(
        db.BigInteger()
            .with_variant(db.Integer, "sqlite")
            .with_variant(MySQL_BIGINT(unsigned=True), "mysql"),
        db.ForeignKey('expenses.expense_id', ondelete='SET NULL'),
        nullable=True
)
    # Foreign key to Invoice
    invoice_id = db.Column(
        db.BigInteger().with_variant(db.Integer, "sqlite").with_variant(MySQL_BIGINT(unsigned=True), "mysql"),
        db.ForeignKey('invoices.invoice_id', ondelete='SET NULL'),
        nullable=True
)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, onupdate=datetime.utcnow)
    
    # Relationships
    case = db.relationship("Case", back_populates="time_entries")
    expense = db.relationship("Expense", back_populates="time_entries")
    billed_by_attorney = db.relationship("Attorney", backref="time_entries")
    billed_by_employee = db.relationship("Employee", backref="time_entries")

    __table_args__ = (
        CheckConstraint(
            '(billed_by_attorney_id IS NOT NULL AND billed_by_employee_id IS NULL) OR '
            '(billed_by_attorney_id IS NULL AND billed_by_employee_id IS NOT NULL)',
            name='check_single_billed_by'
        ),
        CheckConstraint('duration > 0', name='positive_duration'),
    )
    
class DocumentTag(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    document_id = db.Column(db.Integer, db.ForeignKey('document.id'), nullable=False)
    tag = db.Column(db.String(100), nullable=False)  # e.g., "Relevant", "Timeline Evidence"
    description = db.Column(db.Text, nullable=True)  # explanation
    created_by = db.Column(db.String(50), nullable=False)  # user_id
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())

    document = db.relationship('Document', backref='tags')
