import pytest
from case_management.models import Invoice
from datetime import datetime


# -------------------------------
# GET /api/invoices (auth + pagination)
# -------------------------------

from unittest.mock import patch, Magic<PERSON><PERSON>

def test_get_invoices_as_attorney(client):
    with client.session_transaction() as sess:
        sess["username"] = "da"
        sess["user_role"] = "attorney"
        sess["attorney_id"] = "ATC82FA4EE"

    mock_result = {
        "items": [{"invoice_id": 1, "description": "Mock Invoice"}],
        "meta": {"page": 1, "total_items": 1}
    }

    with patch("case_management.routes.InvoiceRepository.get_all_query") as mock_query, \
         patch("case_management.routes.Paginator.paginate", return_value=mock_result), \
         patch("case_management.routes.InvoiceRepository.append_expenses_to_invoices", return_value=mock_result["items"]):
        
        response = client.get("/api/invoices?case_id=C12345&client_id=CL1001")
        assert response.status_code == 200
        data = response.get_json()
        assert "data" in data
        assert data["pagination"]["total_items"] == 1


def test_get_invoices_unauthenticated(client):
    response = client.get("/api/invoices")
    assert response.status_code == 401

def test_get_invoices_with_invalid_case(client):
    with client.session_transaction() as sess:
        sess["username"] = "da"
        sess["user_role"] = "attorney"
        sess["attorney_id"] = "ATC82FA4EE"

    response = client.get("/api/invoices?case_id=INVALID")
    assert response.status_code in [400, 403, 500]  # It could change according to business logic 

# -------------------------------
# POST /api/invoices
# -------------------------------
from unittest.mock import patch

@patch("case_management.routes.generate_invoice_pdf")
@patch("case_management.routes.InvoiceRepository.create_invoice_ifowner")
@patch("case_management.routes.validators.validate_invoice_payload")
def test_create_invoice_valid(mock_validate, mock_create_invoice, mock_generate_pdf, client):
    with client.session_transaction() as sess:
        sess["username"] = "da"
        sess["user_role"] = "attorney"
        sess["attorney_id"] = "ATC82FA4EE"

    # ✅ Mock validation result
    mock_validate.return_value = {
        "case_id": "C12345",
        "description": "Mocked create",
        "due_date": "2025-08-13"
    }

    mock_invoice = MagicMock(invoice_id=1, shared=False)
    mock_create_invoice.return_value = mock_invoice
    mock_generate_pdf.return_value = {
        "pdf_bytes": b"%PDF-1.4...",
        "filename": "mock_invoice.pdf",
        "file_path": "/tmp/mock_invoice.pdf",
        "case_id": "C12345"
    }

    response = client.post("/api/invoices", json={
        "case_id": "C12345",
        "description": "Mocked create",
        "due_date": "2025-08-13"
    })

    assert response.status_code == 201



def test_create_invoice_missing_data(client):
    with client.session_transaction() as sess:
        sess["username"] = "da"
        sess["user_role"] = "attorney"
        sess["attorney_id"] = "ATC82FA4EE"

    # Missing required fields
    invoice_data = {
        "description": "Missing case_id and due_date"
    }

    response = client.post("/api/invoices", json=invoice_data)
    assert response.status_code == 400
    assert "error" in response.get_json()

# -------------------------------
# PUT /api/invoices/<id>
# -------------------------------

@patch("case_management.routes.InvoiceRepository.update_invoice")
@patch("case_management.routes.InvoiceRepository.get_invoice_if_owner")
@patch("case_management.routes.validators.validate_invoice_payload")
def test_update_invoice_valid(mock_validate, mock_get_invoice, mock_update_invoice, client):
    mock_invoice = MagicMock(invoice_id=1, filename="mock.pdf", shared=False)
    mock_get_invoice.return_value = mock_invoice
    mock_update_invoice.return_value = mock_invoice

    mock_validate.return_value = {
        "description": "Updated via patch",
        "due_date": "2025-09-15",
        "updated_by_id": "ATC82FA4EE",
        "updated_by_type": "attorney"
    }

    with client.session_transaction() as sess:
        sess["username"] = "da"
        sess["user_role"] = "attorney"
        sess["attorney_id"] = "ATC82FA4EE"

    response = client.put("/api/invoices/1", json={
        "description": "Updated via patch",
        "due_date": "2025-09-15"
    })

    assert response.status_code == 200
    assert "Invoice updated successfully" in response.get_json()["message"]



# -------------------------------
# DELETE /api/invoices/<id>
# -------------------------------

@patch("case_management.routes.InvoiceRepository.get_invoice_if_owner")
@patch("case_management.routes.InvoiceRepository.delete_invoice")
def test_delete_invoice_valid(mock_delete_invoice, mock_get_invoice, client):
    mock_get_invoice.return_value = MagicMock(invoice_id=1)

    with client.session_transaction() as sess:
        sess["username"] = "da"
        sess["user_role"] = "attorney"
        sess["attorney_id"] = "ATC82FA4EE"

    response = client.delete("/api/invoices/1", json={"dummy": "data"})
    assert response.status_code == 200
    assert "message" in response.get_json()


# -------------------------------
# persistence data
# -------------------------------

#Test for persistence in db
@patch("case_management.routes.InvoiceRepository.create_invoice_ifowner")
@patch("case_management.routes.validators.validate_invoice_payload")
def test_create_invoice_persists_in_db(mock_validate, mock_create_invoice, client):
    with client.session_transaction() as sess:
        sess["username"] = "da"
        sess["user_role"] = "attorney"
        sess["attorney_id"] = "ATC82FA4EE"

    mock_validate.return_value = {
        "case_id": "C12345",
        "description": "Mocked Persistence",
        "due_date": "2025-08-20",
        "created_by_id": "ATC82FA4EE",
        "created_by_type": "attorney",
        "shared": False
    }

    mock_invoice = MagicMock(invoice_id=123, description="Mocked Persistence")
    mock_create_invoice.return_value = mock_invoice

    response = client.post("/api/invoices", json={
        "case_id": "C12345",
        "description": "Mocked Persistence",
        "due_date": "2025-08-20"
    })

    assert response.status_code == 201
    assert response.get_json()["invoice_id"] == 123


@patch("case_management.routes.InvoiceRepository.get_invoice_if_owner")
@patch("case_management.routes.InvoiceRepository.delete_invoice")
def test_delete_invoice_removes_from_db(mock_delete_invoice, mock_get_invoice, client):
    mock_invoice = MagicMock(invoice_id=999)
    mock_get_invoice.return_value = mock_invoice

    with client.session_transaction() as sess:
        sess["username"] = "da"
        sess["user_role"] = "attorney"
        sess["attorney_id"] = "ATC82FA4EE"

    response = client.delete("/api/invoices/999", json={"dummy": "data"})
    assert response.status_code == 200


@patch("case_management.routes.InvoiceRepository.get_invoice_if_owner", side_effect=PermissionError("Not owner"))
def test_update_invoice_unauthorized_user(mock_check_owner, client):
    with client.session_transaction() as sess:
        sess["username"] = "not_owner"
        sess["user_role"] = "attorney"
        sess["attorney_id"] = "OTHER_ID"

    response = client.put("/api/invoices/1", json={"description": "Should fail"})
    assert response.status_code == 403


@patch("case_management.routes.InvoiceRepository.get_invoice_if_owner", side_effect=PermissionError("Not owner"))
def test_delete_invoice_unauthorized_user(mock_check_owner, client):
    with client.session_transaction() as sess:
        sess["username"] = "not_owner"
        sess["user_role"] = "attorney"
        sess["attorney_id"] = "OTHER_ID"

    response = client.delete("/api/invoices/1", json={"dummy": "data"})
    assert response.status_code == 403


