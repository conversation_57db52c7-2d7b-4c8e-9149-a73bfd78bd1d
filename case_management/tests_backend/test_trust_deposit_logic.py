import pytest
from datetime import datetime
from flask import url_for, json
from unittest.mock import patch, MagicMock

@pytest.fixture(autouse=True)
def mock_send_invoice_email(monkeypatch):
    def fake_send_invoice_email(*args, **kwargs):
        return True, None  # Simulate success
    monkeypatch.setattr("case_management.routes.send_invoice_email", fake_send_invoice_email)

def setup_user_session(client, role="client", user_id="CL1001", username="johndoe"):
    with client.session_transaction() as sess:
        sess["username"] = username
        sess["user_role"] = role
        if role == "attorney":
            sess["attorney_id"] = user_id
        elif role == "employee":
            sess["employee_id"] = user_id
        elif role == "client":
            sess["client_id"] = user_id
        else:
            sess["user_id"] = user_id

def test_not_new_paid_invoice_on_activity_log(client, mocker):
    setup_user_session(client, role="attorney", user_id="ATC82FA4EE")

    new_activity_log = {
        "case_id": "C12345",
        "activity_type": "research",
        "time_spent": 5,
        "description": "Description 1"
    }

    # Track if passes invoice_id logic
    mock_invoice_id = mocker.patch("case_management.routes.InvoiceRepository.get_invoice_by_id")

    response = client.post("/log-activity", json=new_activity_log)
    assert response.status_code == 201
    json_data = response.get_json()
    assert not mock_invoice_id.called
    assert "mail_response" in json_data
    assert json_data["mail_response"] == "No invoice assigned on this activity log"

def test_not_new_paid_invoice_on_expense(client, mocker):
    setup_user_session(client, role="attorney", user_id="ATC82FA4EE")

    new_expense = {
        "case_id": "C12345",
        "amount": 353.0,
        "title": "Titulo Prueeeba",
        "description": "Legal consulting invoice"
    }

    # Track if passes invoice_id logic
    mock_invoice_id = mocker.patch("case_management.routes.InvoiceRepository.get_invoice_by_id")

    response = client.post("/api/expenses", json=new_expense)
    assert response.status_code == 201
    json_data = response.get_json()
    assert not mock_invoice_id.called
    assert "mail_response" in json_data
    assert json_data["mail_response"] == "No invoice assigned on this expense"

def test_no_new_invoice_on_paid_activity_log(client):
    setup_user_session(client, role="attorney", user_id="ATC82FA4EE")

    new_activity_log = {
        "case_id": "CASE_CL1002",
        "activity_type": "research",
        "time_spent": 5,
        "description": "Description 1"
    }

    response = client.post("/log-activity", json=new_activity_log)

    assert response.status_code == 201
    json_data = response.get_json()

    # Verify invoice
    assert "mail_response" in json_data
    assert json_data["mail_response"] == "No invoice assigned on this activity log"

    from case_management.models import ActivityLog, Transfer
    from case_management.extensions import db

    activity_id = json_data["activity_id"]
    activity = db.session.get(ActivityLog, activity_id)
    transfer = db.session.query(Transfer).filter_by(activity_id=activity.id).first()

    assert activity.invoice_id is None
    assert transfer and transfer.amount == activity.total_amount

def test_no_new_invoice_on_paid_expense(client):
    setup_user_session(client, role="attorney", user_id="ATC82FA4EE")

    new_expense = {
        "case_id": "CASE_CL1002",
        "amount": 353.0,
        "title": "Titulo Prueeeba",
        "description": "Legal consulting invoice"
    }

    response = client.post("/api/expenses", json=new_expense)

    assert response.status_code == 201
    json_data = response.get_json()

    # Verify invoice
    assert "mail_response" in json_data
    assert json_data["mail_response"] == "No invoice assigned on this expense"

    from case_management.models import Expense, Transfer
    from case_management.extensions import db

    expense_id = json_data["expense_id"]
    expense = db.session.get(Expense, expense_id)
    transfer = db.session.query(Transfer).filter_by(expense_id=expense.expense_id).first()

    assert expense.invoice_id is None
    assert transfer and transfer.amount == expense.amount

def test_new_unpaid_invoice_on_expense(client):
    setup_user_session(client, role="employee", user_id="3")

    # Trust deposit has 200.00 of allowed amount
    new_expense = {
        "case_id": "case1",
        "amount": 353.0,
        "title": "Titulo Prueeeba",
        "description": "Legal consulting invoice"
    }

    response = client.post("/api/expenses", json=new_expense)
    assert response.status_code == 201
    json_data = response.get_json()

    # Verify invoice
    assert "mail_response" in json_data
    assert json_data["mail_response"] == "Email sent correctly"

    from case_management.models import Expense, Invoice, Transfer, TrustDeposit
    from case_management.extensions import db

    expense_id = json_data["expense_id"]
    expense = db.session.get(Expense, expense_id)
    invoice = db.session.get(Invoice, expense.invoice_id)
    transfer = db.session.query(Transfer).filter_by(expense_id=expense.expense_id).first()
    trust_deposit = db.session.query(TrustDeposit).filter_by(case_id=invoice.case_id).first()

    assert expense.invoice_id is not None
    assert invoice.amount == 153.00 #Outstanding payment
    assert invoice.status == 'unpaid'
    assert transfer and transfer.amount == 200.00
    assert trust_deposit and trust_deposit.amount == 0

def test_new_unpaid_invoice_on_activity_log(client):
    setup_user_session(client, role="employee", user_id="3")

    # Trust deposit has 5.00 of allowed amount
    new_activity_log = {
        "case_id": "case3",
        "activity_type": "research",
        "time_spent": 6,
        "description": "Description 1"
    }

    response = client.post("/log-activity", json=new_activity_log)

    assert response.status_code == 201
    json_data = response.get_json()

    # Verify invoice
    assert "mail_response" in json_data
    assert json_data["mail_response"] == "Email sent correctly"

    from case_management.models import ActivityLog, Invoice, Transfer, TrustDeposit
    from case_management.extensions import db

    activity_id = json_data["activity_id"]
    activity = db.session.get(ActivityLog, activity_id)
    invoice = db.session.get(Invoice, activity.invoice_id)
    transfer = db.session.query(Transfer).filter_by(activity_id=activity.id).first()
    trust_deposit = db.session.query(TrustDeposit).filter_by(case_id=invoice.case_id).first()

    assert activity.invoice_id is not None
    assert invoice.amount == 100.00 #Outstanding payment
    assert invoice.status == 'unpaid'
    assert transfer and transfer.amount == 500.00
    assert trust_deposit and trust_deposit.amount == 0

# Successfully create a new trust depost
def test_create_trust_deposit_success(client):
    setup_user_session(client, role="attorney", user_id="ATC82FA4EE")

    data = {
        "case_id": "C12345",
        "amount": 500.0
    }

    response = client.post("/api/trust-deposits", json=data)
    json_data = response.get_json()

    assert response.status_code == 201
    assert "message" in json_data
    assert json_data["message"] in [
        "Trust deposit created successfully.",
        "Trust deposit already exist for this case, amount updated successfully."
    ]


# Missing amount param
def test_create_trust_deposit_missing_amount(client):
    setup_user_session(client, role="attorney", user_id="ATC82FA4EE")

    data = {
        "case_id": "C12345"
        # missing 'amount'
    }

    response = client.post("/api/trust-deposits", json=data)
    json_data = response.get_json()

    assert response.status_code == 400
    assert "Missing required fields" in json_data["error"]


# Amount 0 or negative
@pytest.mark.parametrize("invalid_amount", [0, -10])
def test_create_trust_deposit_invalid_amount(client, invalid_amount):
    setup_user_session(client, role="attorney", user_id="ATC82FA4EE")

    data = {
        "case_id": "C12345",
        "amount": invalid_amount
    }

    response = client.post("/api/trust-deposits", json=data)
    json_data = response.get_json()

    assert response.status_code == 400
    assert "Amount cannot be zeor or negative" in json_data["error"]


# case_id does not exists
def test_create_trust_deposit_invalid_case_id(client):
    setup_user_session(client, role="attorney", user_id="ATC82FA4EE")

    data = {
        "case_id": "NON_EXISTENT_CASE",
        "amount": 200.0
    }

    response = client.post("/api/trust-deposits", json=data)
    json_data = response.get_json()

    assert response.status_code == 400
    assert "case not found" in json_data["error"]


# employee witrh permission does not have permission on law firm id assigned to that case
def test_create_trust_deposit_wrong_law_firm(client):
    setup_user_session(client, role="employee", user_id="2")

    data = {
        "case_id": "C12345",
        "amount": 150.0
    }

    response = client.post("/api/trust-deposits", json=data)
    json_data = response.get_json()

    assert response.status_code == 403
    assert "permission" in json_data["error"].lower()

# ❌ Attorney does not have access to that case
def test_create_trust_deposit_attorney_not_authorized(client):
    # Case have to exists and be assigned to the attorney
    setup_user_session(client, role="attorney", user_id="AT46EE5FBE")

    response = client.post("/api/trust-deposits", json={"case_id": "C12345", "amount": 500})
    json_data = response.get_json()

    assert response.status_code == 403
    assert "don’t have permission" in json_data["error"].lower()

# Employee successfully create trust deposit for a case assigned to his law fir if permissions
def test_create_trust_deposit_employee_with_permission_success(client):
    setup_user_session(client, role="employee", user_id="3")

    data = {
        "case_id": "C12345",
        "amount": 150.0
    }

    response = client.post("/api/trust-deposits", json=data)
    json_data = response.get_json()

    assert response.status_code == 201
    assert "message" in json_data
    assert json_data["message"] in [
        "Trust deposit created successfully.",
        "Trust deposit already exist for this case, amount updated successfully."
    ]

# Employee exists but does not have permission to create trust deposits
def test_create_trust_deposit_employee_not_permission(client):

    setup_user_session(client, role="employee", user_id="1")

    response = client.post("/api/trust-deposits", json={"case_id": "C12345", "amount": 500})

    assert response.status_code == 403
