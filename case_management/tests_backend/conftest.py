import pytest
import sys, os

# Ensure Python can find the case_management package
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from user_management.config import TestingConfig, Config
from case_management.case_api import create_app
from case_management.extensions import db as _db
from flask import session

@pytest.fixture
def app():
    # Choose Config (MySQL) in CI, TestingConfig (SQLite) otherwise
    config_class = Config if os.getenv("CI") == "true" else TestingConfig
    app = create_app(config_class)

    with app.app_context():
        _db.create_all()

        # Load test data based on database type
        if config_class == TestingConfig:
            # SQLite setup
            sql_file_path = os.path.join(os.path.dirname(__file__), 'test-sqlite-data/test_data.sql')
            with open(sql_file_path, 'r', encoding='utf-8') as f:
                sql_script = f.read()
            
            conn = _db.engine.raw_connection()
            try:
                cursor = conn.cursor() 
                cursor.executescript(sql_script)  # Use cursor instead of conn
                conn.commit()
                cursor.close()
            except Exception as e:
                conn.rollback()
                raise e
            finally:
                conn.close()
        else:
            # MySQL setup - load data programmatically
            _load_mysql_test_data()

        yield app

        # Cleanup
        _db.session.remove()
        _db.drop_all()

def _load_mysql_test_data():
    """Load test data for MySQL database using your .env credentials"""
    from case_management.models import (
        Case, Client, Attorney, LawFirm, TrustDeposit, Employee, 
        CaseDeadline, CaseTask, CustomDocumentType, Document, 
        DocumentVersion, Invoice, Expense, EmployeePermission
    )
    from datetime import datetime
    
    try:
        # Law Firms
        law_firms = [
            {'id': 1, 'name': 'Default Law Firm', 'address': 'Unknown', 'contact_email': '<EMAIL>', 'phone_number': '************'},
            {'id': 2, 'name': 'Test law firm', 'address': 'Unknown2', 'contact_email': '<EMAIL>', 'phone_number': '************'}
        ]
        
        for law_firm_data in law_firms:
            if not LawFirm.query.get(law_firm_data['id']):
                law_firm = LawFirm(**law_firm_data)
                _db.session.add(law_firm)
        _db.session.commit()
        
        # Attorneys
        attorneys = [
            {'attorney_id': 'C752A51B', 'name': 'Test Attorney', 'specialization': 'Criminal Law', 'description': 'Experienced attorney in criminal law', 'username': 'testattorney', 'password_hash': 'scrypt:32768:8:1$5p8pwDJVC0tC7rmv$1f97e3ed9387b5717f849b5872a795769bd7b7473b69cce52c1c58b89dbedfad0e34f34d7f166a1e4f1579bd03e4d03c6282efc40e331c122054d83194489c32', 'email': '<EMAIL>', 'phone_number': '************', 'address': '123 Main St, Springfield, IL, 62704', 'role': '', 'hourly_rate': 0.00, 'law_firm_id': 1, 'is_hourly': True},
            {'attorney_id': 'AT46EE5FBE', 'name': 'Christopher Magno', 'specialization': 'Criminal', 'description': 'fdasdfafdassafdsf', 'username': 'cmagno', 'password_hash': 'scrypt:32768:8:1$NMJqPcwhKodjR8If$010f38cdb9075d42f2acd4f021ff996b9c0a8f6bc1adef81987cedd8ed14e4410712bb777d0b22ab7b361bfd1904a17f7f86d147134fb66e77b71b6e39150a72', 'email': '<EMAIL>', 'phone_number': '************', 'address': '12 Second St, Dallas, TX, 75222', 'role': '', 'hourly_rate': 0.00, 'law_firm_id': 1, 'is_hourly': True},
            {'attorney_id': 'AT41769433', 'name': 'Jane Attorney', 'specialization': 'Corporate Law', 'description': 'Test attorney', 'username': 'jane_attorney', 'password_hash': 'scrypt:32768:8:1$Dhk5HRcul2nDb0Xi$c53af0aec2a6bbee6c23211daf486b5ca1f24d4205349f7a0d265393b0d33356558c73a224d5cdc40af1ab0d122aac709eb8b60d0717dd867ae80ea59717926a', 'email': '<EMAIL>', 'phone_number': '9876543210', 'address': '456 Elm St, Dallas, TX, 75222', 'role': '', 'hourly_rate': 0.00, 'law_firm_id': 1, 'is_hourly': False},
            {'attorney_id': 'attorney1', 'name': 'Jimmy Smith', 'specialization': None, 'description': None, 'username': 'jimmy.smith', 'password_hash': 'scrypt:32768:8:1$o7DWjPLzEde1Gblx$75272181d50bccfab523dc8109c72b48c8585691ffb4ee3a4ca17a8b751d0652b19212b770b6ab5fdd42bef2f603b0a6bc4c92a6beeced046cf965372f83ddf0', 'email': '<EMAIL>', 'phone_number': None, 'address': None, 'role': '', 'hourly_rate': 0.00, 'law_firm_id': 1, 'is_hourly': False},
            {'attorney_id': 'attorney2', 'name': 'Joan Paul', 'specialization': None, 'description': None, 'username': 'joan.paul', 'password_hash': 'scrypt:32768:8:1$zPD4RPJYNgUJ9poS$defa6f3e47608c54883816fcbe4efd3aeb0e626ffe190c35843a9614e58dd304b2d80dcb9c6dde2bc25957b6444e34bf9d6e330660a566ab02b878968b19afa2', 'email': '<EMAIL>', 'phone_number': None, 'address': None, 'role': '', 'hourly_rate': 0.00, 'law_firm_id': 1, 'is_hourly': False},
            {'attorney_id': 'ATC82FA4EE', 'name': 'Daniel Esteban Yepes Morales', 'specialization': 'Lawyer', 'description': 'da', 'username': 'da', 'password_hash': 'scrypt:32768:8:1$B97QKcJWjv0eaxUG$72c8787e75b0a131d8217ce4b8172f569e1840398a310378a826a77d587a38decacca1d816c0efcf8d204598fb044fd52cd0e05a0bac213ba6a4820c17c5acaf', 'email': '<EMAIL>', 'phone_number': None, 'address': '123 Main St, Springfield, IL, 62704', 'role': 'Associate', 'hourly_rate': 1.00, 'law_firm_id': 1, 'is_hourly': True},
            {'attorney_id': 'ATC_WRONG', 'name': 'Daniel Esteban Yepes Morales', 'specialization': 'Lawyer', 'description': 'da2', 'username': 'da2', 'password_hash': 'scrypt:32768:8:1$B97QKcJWjv0eaxUG$72c8787e75b0a131d8217ce4b8172f569e1840398a310378a826a77d587a38decacca1d816c0efcf8d204598fb044fd52cd0e05a0bac213ba6a4820c17c5acaf', 'email': '<EMAIL>', 'phone_number': None, 'address': '123 Main St, Springfield, IL, 62704', 'role': 'Associate', 'hourly_rate': 1.00, 'law_firm_id': 2, 'is_hourly': True}
        ]
        
        for attorney_data in attorneys:
            if not Attorney.query.filter_by(attorney_id=attorney_data['attorney_id']).first():
                attorney = Attorney(**attorney_data)
                _db.session.add(attorney)
        _db.session.commit()
        
        # Clients
        clients = [
            {'client_id': 'CL1001', 'name': 'John Doe', 'username': 'johndoe', 'password_hash': 'scrypt:32768:8:1$n9i3TbkG5runJbFA$46f53c9c6b9e527a92082d9968e0ed247f5e11d7fbdcf23993f977af024a89c5da1b033eff237e63d0778a651baf39e8825421ee3ff822084aacd151f97d2ca8', 'description': 'testing the update feature', 'email': '<EMAIL>', 'phone_number': '************', 'address': '1655 BuckHEAD blvd., Austin, TX, 75002', 'custom_fields': None, 'law_firm_id': 1, 'primary_attorney_id': 'ATC82FA4EE'},
            {'client_id': 'CL1002', 'name': 'Jane Smith', 'username': 'janesmith', 'password_hash': 'hashed_password_1', 'description': 'Second test client', 'email': '<EMAIL>', 'phone_number': None, 'address': None, 'custom_fields': None, 'law_firm_id': 1, 'primary_attorney_id': None},
            {'client_id': 'CL1003', 'name': 'Alice Johnson', 'username': 'alicej', 'password_hash': 'hashed_password_2', 'description': 'Third test client', 'email': '<EMAIL>', 'phone_number': None, 'address': None, 'custom_fields': None, 'law_firm_id': 1, 'primary_attorney_id': None},
            {'client_id': 'CL1004', 'name': 'Bob Williams', 'username': 'bobw', 'password_hash': 'hashed_password_3', 'description': 'Fourth test client', 'email': '<EMAIL>', 'phone_number': None, 'address': None, 'custom_fields': None, 'law_firm_id': 1, 'primary_attorney_id': None},
            {'client_id': 'CBF026903', 'name': 'John Blow', 'username': 'john1738', 'password_hash': 'scrypt:32768:8:1$g398yfRIyzkH4Vv7$f0675e6a788231f6b4b08ee53720d557fedb8e267863da9e668c5bb56b2c92debf8acebc59b9790c21e51180fb1cc1b5b1c76476f1df9d5b7269bc0cbdd1a796', 'description': None, 'email': '<EMAIL>', 'phone_number': None, 'address': None, 'custom_fields': None, 'law_firm_id': 1, 'primary_attorney_id': None},
            {'client_id': 'C60EB4960', 'name': 'Donny Pang', 'username': 'donny9300', 'password_hash': 'scrypt:32768:8:1$ZSsaC14RxF4WWfxP$ac8ceb0b983f13bd7085ed9477e3f05fd2d443f78524b64a1b43e18d731749231d0ac390473859953bc0376e87da852644945b0407eb57c97995e100fa08cffd', 'description': 'fasfdasfadfasfdsa', 'email': '<EMAIL>', 'phone_number': '************', 'address': '123 Second Ave, Decatur, GA, 34566', 'custom_fields': None, 'law_firm_id': 1, 'primary_attorney_id': None},
            {'client_id': 'C5D5EC2F7', 'name': 'Mackey Royale', 'username': 'mackey5234', 'password_hash': 'scrypt:32768:8:1$n4R1Nbze8dFQSpRK$334cf8ddd84af23e21ac8efa81ddd44f3f68f33eb137d54fe34899dc4610620f2b763e8753ffc4cdb69dc2aa8c95c4742005e394cfacda25de28790866722a43', 'description': 'rakjd;akdfmsa;mcm;', 'email': '<EMAIL>', 'phone_number': '************', 'address': '333 Big Butt Drive, Evanston, IL, 30315', 'custom_fields': None, 'law_firm_id': 1, 'primary_attorney_id': None},
            {'client_id': 'CL0D7BFC68', 'name': 'Sweet James Jones', 'username': 'sjones', 'password_hash': 'scrypt:32768:8:1$WnGJe9wAhddwTbtl$11faeae1f6ae33ae1cf1031f0d8c4be767ceea93ea3482024ba1a52e7a98c853a92952d28f516dccf097720b00a3fe7df6dd217a490a274489d5cd11b4b0eebd', 'description': 'the judge wants to give me a year and I said F that', 'email': '<EMAIL>', 'phone_number': '************', 'address': '123 Winslow blvd, Dallas, TX, 75222', 'custom_fields': None, 'law_firm_id': 1, 'primary_attorney_id': None},
            {'client_id': 'CLD17D4DFD', 'name': 'George Washington', 'username': 'gwashington', 'password_hash': 'scrypt:32768:8:1$VyLUPVUm7yhRFnSm$c2c2513dc6d6522e26957421e7caf86d67cbba6934d09adb5fb47b97b1c75d19bf4068b4f608254e91d75d67ef976c2197b151445962a34d7f9bc7c5d02a9ed1', 'description': 'Test client', 'email': '<EMAIL>', 'phone_number': '1234567890', 'address': '123 Main St, Dallas, TX, 75222', 'custom_fields': None, 'law_firm_id': 1, 'primary_attorney_id': None},
            {'client_id': 'CE6362536', 'name': 'Big Hurt', 'username': 'big5727', 'password_hash': 'scrypt:32768:8:1$xSs53N8RdugYrrVz$6ef7fc07513b91150e7a86a40857d55294c1093f4feb1146d5f9219047ceaa1e02e1fcdf39cb8fe4b9e54c81d9da2cbbd9e6575ddfac0f2598a283bfff6d736f', 'description': 'thafdskfaj;sdjkkmjvfamv', 'email': '<EMAIL>', 'phone_number': '************', 'address': '123 Thug st., Dallas, TX, 75111', 'custom_fields': {}, 'law_firm_id': 1, 'primary_attorney_id': None},
            {'client_id': 'C9DA7EAF3', 'name': 'Monster Lockness', 'username': 'monster2181', 'password_hash': 'scrypt:32768:8:1$4IHIYDKx8BxYrwFD$b17ab13db150b25c460b21350427b29b3b2c0d0af14125217af4ee34724f103039ca62bbb1aa8d8781225383cd72ac04754c3951cbbed92ffb2caaff4565351d', 'description': 'Monster', 'email': '<EMAIL>', 'phone_number': '************', 'address': '7899 Wortshire St., Dallas, TX, 75222', 'custom_fields': {"Favorite Food": "Ice Cream"}, 'law_firm_id': 1, 'primary_attorney_id': None},
            {'client_id': 'client1', 'name': 'Robert Brown', 'username': 'robert.brown', 'password_hash': 'hashedpassword1', 'description': None, 'email': '<EMAIL>', 'phone_number': None, 'address': None, 'custom_fields': None, 'law_firm_id': 1, 'primary_attorney_id': None},
            {'client_id': 'client2', 'name': 'Sara Williams', 'username': 'sara.williams', 'password_hash': 'pbkdf2:sha256:1000000$23ReB0G9HETsBE3K$baf7f73ac661bf03bd1cbb2d9cef48991014ff598b9f0b4d4427257524497b74', 'description': None, 'email': '<EMAIL>', 'phone_number': None, 'address': None, 'custom_fields': None, 'law_firm_id': 1, 'primary_attorney_id': None},
            {'client_id': 'client3', 'name': 'Michael Johnson', 'username': 'michael.johnson', 'password_hash': 'hashedpassword3', 'description': None, 'email': '<EMAIL>', 'phone_number': None, 'address': None, 'custom_fields': None, 'law_firm_id': 1, 'primary_attorney_id': None},
            {'client_id': 'client4', 'name': 'Linda Davis', 'username': 'linda.davis', 'password_hash': 'hashedpassword4', 'description': None, 'email': '<EMAIL>', 'phone_number': None, 'address': None, 'custom_fields': None, 'law_firm_id': 1, 'primary_attorney_id': None}
        ]
        
        for client_data in clients:
            if not Client.query.filter_by(client_id=client_data['client_id']).first():
                client = Client(**client_data)
                _db.session.add(client)
        _db.session.commit()
        
        # Employees (create before Cases to satisfy FK constraints)
        employees = [
            {'id': 1, 'name': 'Matt Paul', 'email': '<EMAIL>', 'role': 'Paralegal', 'hourly_rate': 150.00, 'law_firm_id': 1, 'username': 'user_1', 'password_hash': '', 'address': None, 'phone_number': None, 'last_login': None, 'date_added': None, 'is_hourly': True},
            {'id': 2, 'name': 'Bryce Ends', 'email': '<EMAIL>', 'role': 'Attorney', 'hourly_rate': 300.00, 'law_firm_id': 2, 'username': 'user_2', 'password_hash': '', 'address': None, 'phone_number': None, 'last_login': None, 'date_added': None, 'is_hourly': False},
            {'id': 3, 'name': 'Maurice Metts', 'email': '<EMAIL>', 'role': 'Legal Assistant', 'hourly_rate': 100.00, 'law_firm_id': 1, 'username': 'user_3', 'password_hash': '', 'address': None, 'phone_number': None, 'last_login': None, 'date_added': None, 'is_hourly': True}
        ]
        
        for employee_data in employees:
            if not Employee.query.get(employee_data['id']):
                employee = Employee(**employee_data)
                _db.session.add(employee)
        _db.session.commit()

        # Cases
        cases = [
            {'case_id': 'C12345', 'client_id': 'CL1001', 'client_name': 'John Doe', 'case_name': 'Personal Injury Case', 'case_type': 'Civil', 'case_status': 'Open', 'description': 'Test case for John Doe', 'custom_fields': None, 'attorney_id': 'ATC82FA4EE', 'employee_id': None, 'billing_info_id': None},
            {'case_id': 'CASE_CL1002', 'client_id': 'CL1002', 'client_name': 'Jane Smith', 'case_name': 'Jane Smith Case', 'case_type': 'Civil', 'case_status': 'Open', 'description': 'Case for Jane Smith', 'custom_fields': None, 'attorney_id': 'ATC82FA4EE', 'employee_id': None, 'billing_info_id': None},
            {'case_id': 'CASE_CL1003', 'client_id': 'CL1003', 'client_name': 'Alice Johnson', 'case_name': 'Alice Johnson Case', 'case_type': 'Civil', 'case_status': 'Open', 'description': 'Case for Alice Johnson', 'custom_fields': None, 'attorney_id': 'attorney1', 'employee_id': None, 'billing_info_id': None},
            {'case_id': 'CASE_CL1004', 'client_id': 'CL1004', 'client_name': 'Bob Williams', 'case_name': 'Bob Williams Case', 'case_type': 'Civil', 'case_status': 'Open', 'description': 'Case for Bob Williams', 'custom_fields': None, 'attorney_id': 'attorney1', 'employee_id': None, 'billing_info_id': None},
            {'case_id': '123-XYZ', 'client_id': 'CL1001', 'client_name': 'John Doe', 'case_name': 'Doe v. Roe', 'case_type': 'Civil', 'case_status': 'Open', 'description': 'fdkaj;fdjkasdjklasklfmjc', 'custom_fields': None, 'attorney_id': 'attorney1', 'employee_id': None, 'billing_info_id': None},
            {'case_id': 'CASE-9AEDF933', 'client_id': 'C5D5EC2F7', 'client_name': 'Mackey Royale', 'case_name': 'State v. Royale', 'case_type': 'transactional', 'case_status': 'Open', 'description': 'fdkaj;fdjkasdjklasklfmjc', 'custom_fields': None, 'attorney_id': 'attorney1', 'employee_id': None, 'billing_info_id': None},
            {'case_id': 'CASE-7068C7AF', 'client_id': 'C5D5EC2F7', 'client_name': 'Mackey Royale', 'case_name': 'Royale v. Jenkins', 'case_type': 'suit', 'case_status': 'Open', 'description': '2nd case for Royale', 'custom_fields': None, 'attorney_id': 'attorney1', 'employee_id': None, 'billing_info_id': None},
            {'case_id': 'CASE-0EFB6397', 'client_id': 'CL1001', 'client_name': 'John Doe', 'case_name': 'Doe v. Bro', 'case_type': 'Corporate', 'case_status': 'Open', 'description': 'fdasfdasfdafddfdsafsa', 'custom_fields': None, 'attorney_id': 'attorney1', 'employee_id': None, 'billing_info_id': None},
            {'case_id': 'CASE-12345678', 'client_id': 'CL1001', 'client_name': 'John Doe', 'case_name': 'Test Case', 'case_type': 'Civil', 'case_status': 'Open', 'description': 'This is a test case.', 'custom_fields': None, 'attorney_id': 'attorney1', 'employee_id': None, 'billing_info_id': None},
            {'case_id': 'case1', 'client_id': 'client1', 'client_name': 'Robert Brown', 'case_name': 'Personal Injury Case', 'case_type': 'Civil', 'case_status': 'Open', 'description': None, 'custom_fields': None, 'attorney_id': None, 'employee_id': 3, 'billing_info_id': None},
            {'case_id': 'case2', 'client_id': 'client1', 'client_name': 'Robert Brown', 'case_name': 'Property Dispute', 'case_type': 'Civil', 'case_status': 'Closed', 'description': None, 'custom_fields': None, 'attorney_id': 'attorney1', 'employee_id': None, 'billing_info_id': None},
            {'case_id': 'case3', 'client_id': 'client2', 'client_name': 'Sara Williams', 'case_name': 'Divorce Proceedings', 'case_type': 'Family', 'case_status': 'Open', 'description': None, 'custom_fields': None, 'attorney_id': None, 'employee_id': 3, 'billing_info_id': None},
            {'case_id': 'case4', 'client_id': 'client2', 'client_name': 'Sara Williams', 'case_name': 'Child Custody', 'case_type': 'Family', 'case_status': 'Pending', 'description': None, 'custom_fields': None, 'attorney_id': 'attorney1', 'employee_id': None, 'billing_info_id': None},
            {'case_id': 'case5', 'client_id': 'client3', 'client_name': 'Michael Johnson', 'case_name': 'Criminal Defense', 'case_type': 'Criminal', 'case_status': 'Open', 'description': None, 'custom_fields': None, 'attorney_id': None, 'employee_id': 3, 'billing_info_id': None},
            {'case_id': 'case6', 'client_id': 'client3', 'client_name': 'Michael Johnson', 'case_name': 'Drug Possession', 'case_type': 'Criminal', 'case_status': 'Closed', 'description': None, 'custom_fields': None, 'attorney_id': 'attorney1', 'employee_id': None, 'billing_info_id': None},
            {'case_id': 'case7', 'client_id': 'client4', 'client_name': 'Linda Davis', 'case_name': 'Contract Dispute', 'case_type': 'Civil', 'case_status': 'Open', 'description': None, 'custom_fields': None, 'attorney_id': 'attorney1', 'employee_id': None, 'billing_info_id': None},
            {'case_id': 'case8', 'client_id': 'client4', 'client_name': 'Linda Davis', 'case_name': 'Employment Discrimination', 'case_type': 'Employment', 'case_status': 'Pending', 'description': None, 'custom_fields': None, 'attorney_id': 'attorney1', 'employee_id': None, 'billing_info_id': None}
        ]

        # After employees are created, add:
        employee_permissions = [
            {'employee_id': 3, 'permission_name': 'write_permission'},
            {'employee_id': 3, 'permission_name': 'create_trust_deposit'},
        ]

        for perm in employee_permissions:
            if not EmployeePermission.query.filter_by(employee_id=perm['employee_id'], permission_name=perm['permission_name']).first():
                permission = EmployeePermission(**perm)
                _db.session.add(permission)
        _db.session.commit()

        for case_data in cases:
            if not Case.query.filter_by(case_id=case_data['case_id']).first():
                case = Case(**case_data)
                _db.session.add(case)
        _db.session.commit()

        # Case Deadlines
        deadlines = [
            {'case_id': 'CASE-12345678', 'deadline_date': datetime(2025, 3, 15, 14, 30, 0), 'deadline_type': 'trial', 'notes': 'Trial deadline.'}
        ]
        
        for deadline_data in deadlines:
            if not CaseDeadline.query.filter_by(case_id=deadline_data['case_id'], deadline_type=deadline_data['deadline_type']).first():
                deadline = CaseDeadline(**deadline_data)
                _db.session.add(deadline)
        _db.session.commit()
        
        # Case Tasks
        tasks = [
            {'case_id': 'CASE-12345678', 'task_type': 'legal drafting', 'description': 'Draft contract', 'due_date': datetime(2025, 3, 10, 9, 0, 0), 'status': 'pending', 'priority': 'medium', 'assigned_to_employee_id': None, 'assigned_to_client_id': None}
        ]
        
        for task_data in tasks:
            if not CaseTask.query.filter_by(case_id=task_data['case_id'], task_type=task_data['task_type']).first():
                task = CaseTask(**task_data)
                _db.session.add(task)
        _db.session.commit()
        
        # Custom Document Types
        custom_doc_types = [
            {'type_name': 'Deposition Testimony', 'created_by': 'attorney1', 'created_at': datetime(2025, 3, 10, 18, 17, 51)},
            {'type_name': 'Affidavit', 'created_by': 'attorney1', 'created_at': datetime(2025, 3, 10, 18, 41, 33)},
            {'type_name': 'test documents', 'created_by': 'client:client2', 'created_at': datetime(2025, 3, 15, 22, 47, 42)}
        ]
        
        for doc_type_data in custom_doc_types:
            if not CustomDocumentType.query.filter_by(type_name=doc_type_data['type_name']).first():
                doc_type = CustomDocumentType(**doc_type_data)
                _db.session.add(doc_type)
        _db.session.commit()
        
        # Documents
        documents = [
            {'filename': '92f1343a-6e03-4453-8eee-6fb5ae5c506e_test1.pdf', 'original_name': 'test1.pdf', 'case_id': 'C12345', 'client_id': 'CL1001', 'uploaded_at': datetime(2025, 2, 2, 17, 34, 44), 'document_type': '', 'custom_document_type_id': None, 'uploaded_by': ''},
            {'filename': '9524139a-715f-4c29-bf91-144ea2463817_test1.pdf', 'original_name': 'test1.pdf', 'case_id': 'C12345', 'client_id': 'CL1001', 'uploaded_at': datetime(2025, 2, 14, 21, 49, 56), 'document_type': '', 'custom_document_type_id': None, 'uploaded_by': ''},
            {'filename': '106008d8-f2b6-4222-bda8-b21834ab7ab9_test2.pdf', 'original_name': 'test2.pdf', 'case_id': 'CASE_CL1002', 'client_id': 'CL1002', 'uploaded_at': datetime(2025, 2, 14, 21, 51, 35), 'document_type': '', 'custom_document_type_id': None, 'uploaded_by': ''},
            {'filename': '899675b4-d47f-4c67-911f-065d7f00c2c6_test1.pdf', 'original_name': 'test1.pdf', 'case_id': 'C12345', 'client_id': 'CL1001', 'uploaded_at': datetime(2025, 2, 15, 18, 23, 52), 'document_type': '', 'custom_document_type_id': None, 'uploaded_by': ''},
            {'filename': '768b692b-1c49-4129-9b7c-0f98710bcae4_test1.pdf', 'original_name': 'test1.pdf', 'case_id': 'case3', 'client_id': 'client2', 'uploaded_at': datetime(2025, 3, 9, 16, 11, 21), 'document_type': '', 'custom_document_type_id': None, 'uploaded_by': ''},
            {'filename': '8e74177d-a901-4719-b838-2969e9dc7e71_test1.pdf', 'original_name': 'test1.pdf', 'case_id': 'case3', 'client_id': 'client2', 'uploaded_at': datetime(2025, 3, 10, 17, 39, 32), 'document_type': 'discovery', 'custom_document_type_id': None, 'uploaded_by': 'jimmy.smith'},
            {'filename': '873f59c2-fc24-40b3-b03a-f8c1ba987a68_test1.pdf', 'original_name': 'test1.pdf', 'case_id': 'case3', 'client_id': 'client2', 'uploaded_at': datetime(2025, 3, 10, 17, 39, 32), 'document_type': 'discovery', 'custom_document_type_id': None, 'uploaded_by': 'jimmy.smith'},
            {'filename': 'cde52f49-3d4f-4b26-a9a8-9d0cb7db4ee0_test1.pdf', 'original_name': 'test1.pdf', 'case_id': 'case3', 'client_id': 'client2', 'uploaded_at': datetime(2025, 3, 10, 18, 17, 51), 'document_type': 'Deposition Testimony', 'custom_document_type_id': None, 'uploaded_by': 'attorney1'},
            {'filename': 'a408a7d5-2ec2-4fdc-990c-105b9165e181_test1.pdf', 'original_name': 'test1.pdf', 'case_id': 'case3', 'client_id': 'client2', 'uploaded_at': datetime(2025, 3, 10, 18, 41, 33), 'document_type': 'Affidavit', 'custom_document_type_id': None, 'uploaded_by': 'attorney1'},
            {'filename': '75a46c05-76e2-4272-96b1-7fa108fb3d23_test1.pdf', 'original_name': 'test1.pdf', 'case_id': 'case3', 'client_id': 'client2', 'uploaded_at': datetime(2025, 3, 10, 18, 41, 33), 'document_type': 'Affidavit', 'custom_document_type_id': None, 'uploaded_by': 'attorney1'},
            {'filename': '3e59f1f5-1226-4f5e-9cf6-6db7b1bafe83_test1.pdf', 'original_name': 'test1.pdf', 'case_id': 'case3', 'client_id': 'client2', 'uploaded_at': datetime(2025, 3, 12, 20, 27, 54), 'document_type': 'evidence-(video)', 'custom_document_type_id': None, 'uploaded_by': 'client:client2'},
            {'filename': 'c060d8b7-309d-4c00-9690-bccfd6beb1ff_test1.pdf', 'original_name': 'test1.pdf', 'case_id': 'case3', 'client_id': 'client2', 'uploaded_at': datetime(2025, 3, 12, 20, 27, 54), 'document_type': 'evidence-(video)', 'custom_document_type_id': None, 'uploaded_by': 'client:client2'},
            {'filename': 'e22a1b75-a378-4f9f-9523-000ce3037d30_test2.pdf', 'original_name': 'test2.pdf', 'case_id': 'case3', 'client_id': 'client2', 'uploaded_at': datetime(2025, 3, 12, 20, 36, 40), 'document_type': 'evidence-(video)', 'custom_document_type_id': None, 'uploaded_by': 'client:client2'}
        ]
        
        for doc_data in documents:
            if not Document.query.filter_by(filename=doc_data['filename']).first():
                document = Document(**doc_data)
                _db.session.add(document)
        _db.session.commit()
        
        # Document Versions
        doc_versions = [
            {'document_id': 5, 'filename': '218fe448-84e3-4f2c-be67-eb4063000f1f_test1.pdf', 'version_number': 1, 'uploaded_by': 'client:client2', 'uploaded_at': datetime(2025, 3, 15, 22, 47, 42)},
            {'document_id': 5, 'filename': '85a33a7c-7c43-40c9-a2d7-b91c9f6c5e5c_test1.pdf', 'version_number': 2, 'uploaded_by': 'client:client2', 'uploaded_at': datetime(2025, 3, 15, 22, 47, 43)}
        ]
        
        for doc_version_data in doc_versions:
            if not DocumentVersion.query.filter_by(filename=doc_version_data['filename']).first():
                doc_version = DocumentVersion(**doc_version_data)
                _db.session.add(doc_version)
        _db.session.commit()
        
        # Trust Deposits
        trust_deposits = [
            {'client_id': 'CL1002', 'case_id': 'CASE_CL1002', 'amount': 570.00},
            {'client_id': 'client1', 'case_id': 'case1', 'amount': 200.00},
            {'client_id': 'client1', 'case_id': 'case3', 'amount': 500.00}
        ]
        
        for trust_data in trust_deposits:
            if not TrustDeposit.query.filter_by(client_id=trust_data['client_id'], case_id=trust_data['case_id']).first():
                trust_deposit = TrustDeposit(**trust_data)
                _db.session.add(trust_deposit)
        _db.session.commit()
        
        # Note: The invoice data in the SQL has syntax errors, so I'll create a basic structure
        # You may need to adjust the Invoice model fields based on your actual schema
        # The SQL shows incomplete INSERT statements for invoices
        
    except Exception as e:
        _db.session.rollback()
        raise e

@pytest.fixture
def client(app):
    """Create a test client for the Flask app"""
    return app.test_client()

@pytest.fixture
def db_session(app):
    """Provide a database session for tests"""
    with app.app_context():
        yield _db.session