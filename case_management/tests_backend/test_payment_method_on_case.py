import json
import pytest
from datetime import date

def setup_user_session(client, role="attorney", user_id="ATC82FA4EE", username="test_user"):
    """
    Helper to set session cookies for authenticated user.
    """
    with client.session_transaction() as sess:
        sess["username"] = username
        sess["user_role"] = role
        # if attorney, set attorney_id
        if role == "attorney":
            sess["attorney_id"] = user_id
        # if employee, set user_id
        elif role == "employee":
            sess["employee_id"] = user_id
        # if client, set user_id
        elif role == "client":
            sess["client_id"] = user_id
        else:
            sess["user_id"] = user_id

def test_post_add_case_unauthorized(client):
    response = client.post('/api/add-case')
    assert response.status_code in [401, 403]


def test_post_add_case_forbidden_wrong_role(client):
    setup_user_session(client, role="client", user_id="CL1001")
    response = client.post('/api/add-case')
    print(f"Response data: {response.get_data()}")
    assert response.status_code == 403


def test_get_work_periods_no_permission_employee(client):
    setup_user_session(client, role="employee", user_id="1")
    response = client.post('/api/add-case')
    print(f"Response data: {response.get_data()}")
    assert response.status_code == 403

from unittest.mock import patch, MagicMock

@patch("repositories.case_task_repository.CaseTaskRepository.add_task")
@patch("repositories.case_deadline_repository.CaseDeadlineRepository.add_deadline")
@patch("repositories.case.CaseRepository.create_case")
@patch("repositories.client.ClientRepository.get_client_by_name")
def test_add_case_payment_method_succes(mock_get_client, mock_create_case, mock_add_deadline, mock_add_task, client):
    setup_user_session(client, role="attorney", user_id="ATC82FA4EE")

    # Mock existing client
    mock_client = MagicMock()
    mock_client.client_id = "CL1001"
    mock_client.name = "John Doe"
    mock_get_client.return_value = mock_client

    # Mock case creation
    mock_case = MagicMock()
    mock_case.case_id = "CASE-1234"
    mock_create_case.return_value = mock_case

    payload = {
        "client_name": "John Doe",
        "case_name": "Caso Prueba",
        "case_type": "Civil",
        "case_status": "Open",
        "description": "Caso de prueba para el cliente",
        "custom_fields": {
            "prioridad": "Alta",
            "referencia_externa": "REF-12345"
        },
        "deadlines": [
            {
                "deadline_date": "2025-05-01",
                "deadline_type": "Entrega Documentos",
                "notes": "Debe entregarse antes del mediodía"
            }
        ],
        "tasks": [
            {
                "task_type": "Revisión",
                "description": "Revisar documentos",
                "due_date": "2025-04-25",
                "status": "Pendiente"
            }
        ],
        "billing_method": "credit_card"
    }

    response = client.post('/api/add-case', json=payload)
    assert response.status_code == 201
    assert response.get_json()["case_id"] == "CASE-1234"


@patch("repositories.client.ClientRepository.get_client_by_name")
def test_add_payment_method_not_allowed_billing_info(mock_get_client, client):
    setup_user_session(client, role="attorney", user_id="ATC82FA4EE")

    mock_client = MagicMock()
    mock_client.client_id = "CL1001"
    mock_client.name = "John Doe"
    mock_get_client.return_value = mock_client

    payload = {
        "client_name": "John Doe",
        "case_name": "Caso Prueba",
        "case_type": "Civil",
        "case_status": "Open",
        "description": "Caso de prueba para el cliente",
        "custom_fields": {
            "prioridad": "Alta",
            "referencia_externa": "REF-12345"
        },
        "deadlines": [
            {
                "deadline_date": "2025-05-01",
                "deadline_type": "Entrega Documentos",
                "notes": "Debe entregarse antes del mediodía"
            }
        ],
        "tasks": [
            {
                "task_type": "Revisión",
                "description": "Revisar documentos",
                "due_date": "2025-04-25",
                "status": "Pendiente"
            }
        ],
        "billing_method": "credit_cardddd"
    }

    response = client.post('/api/add-case', json=payload)
    assert response.status_code == 400
    assert "Invalid billing method" in response.get_json()["error"]

