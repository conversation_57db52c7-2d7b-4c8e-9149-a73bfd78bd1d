import pytest
from unittest.mock import patch
from datetime import date


class FakeAttorney:
    def __init__(self, law_firm_id):
        self.law_firm_id = law_firm_id
        self.hourly_rate = 100


def setup_user_session(client, role="attorney", user_id="ATC82FA4EE", username="test_user"):
    """Helper to set session cookies for authenticated user."""
    with client.session_transaction() as sess:
        sess["username"] = username
        sess["user_role"] = role
        if role == "attorney":
            sess["attorney_id"] = user_id
        elif role == "employee":
            sess["employee_id"] = user_id
        elif role == "client":
            sess["client_id"] = user_id
        else:
            sess["user_id"] = user_id


from datetime import date
from unittest.mock import MagicMock

from unittest.mock import MagicMock
from datetime import date

@patch("case_management.routes.calculate_payout_summary")
@patch("repositories.attorney.AttorneyRepository.get_attorney_by_id")
@patch("utils.get_payout_data.get_user_payout_data")
@patch("utils.shared.validators.validate_payout_payload")
@patch("utils.shared.validators.extract_payout_payload_from_request")
@patch("utils.shared.validators.parse_payload_dates", return_value=("2024-07-01", "2024-07-31"))
def test_get_work_periods_csv_export(mock_parse, mock_extract, mock_validate, mock_get_data, mock_attorney, mock_summary, client):
    setup_user_session(client, role="attorney", user_id="ATC82FA4EE")

    mock_attorney.return_value = FakeAttorney(law_firm_id=1)

    mock_extract.return_value = {
        "user_role": "attorney",
        "user_id": "ATC82FA4EE",
        "start_str": "2024-07-01",
        "end_str": "2024-07-31",
        "export": "csv"
    }

    # Even if mock_get_data returns anything, it's mock_summary that decides the response
    mock_summary.return_value = {
        "work_periods": [{
            "id": 1,
            "start": "2024-07-01",
            "end": "2024-07-07",
            "hours_worked": 8,
            "pay_rate": 100,
            "gross_pay": 800,
            "total_bonus": 0,
            "total_deduction": 0,
            "net_pay": 800
        }],
        "total_hours_worked": 8,
        "total_net_pay": 800,
        "total_bonus": 0,
        "total_deduction": 0,
        "total_gross_pay": 800,
        "payroll_adjustments": []
    }

    response = client.get("/api/payout/work-periods")

    assert response.status_code == 200
    assert response.mimetype == "text/csv"
    assert "hours_worked" in response.data.decode()





def test_get_work_periods_unauthorized_no_session(client):
    response = client.get('/api/payout/work-periods')
    assert response.status_code in [401, 403]


def test_get_work_periods_forbidden_wrong_role(client):
    setup_user_session(client, role="client", user_id="CL1001")
    response = client.get('/api/payout/work-periods')
    assert response.status_code == 403


def test_get_work_periods_no_permission_employee(client):
    setup_user_session(client, role="employee", user_id="1")
    response = client.get('/api/payout/work-periods')
    assert response.status_code == 403


def test_get_work_periods_invalid_date_format(client):
    setup_user_session(client, role="attorney", user_id="ATC82FA4EE")

    bad_payload = {
        "user_role": "attorney",
        "user_id": "ATC82FA4EE",
        "start_str": "January 1st, 2024",  # Invalid date
        "end_str": "2024-07-31"
    }

    response = client.get('/api/payout/work-periods', query_string=bad_payload)
    assert response.status_code == 400

    try:
        data = response.get_json()
        assert "start_str" in data["error"]
    except Exception:
        assert b"Invalid format for start_str" in response.data


@patch("repositories.attorney.AttorneyRepository.get_attorney_by_id")
@patch("utils.get_payout_data.get_user_payout_data")
@patch("utils.shared.validators.validate_payout_payload")
@patch("utils.shared.validators.extract_payout_payload_from_request")
@patch("utils.shared.validators.parse_payload_dates", return_value=("2024-01-01", "2024-01-31"))
def test_get_empty_work_periods(mock_parse, mock_extract, mock_validate, mock_get_data, mock_attorney, client):
    setup_user_session(client, role="attorney", user_id="ATC82FA4EE")

    mock_attorney.return_value = FakeAttorney(law_firm_id=1)

    mock_extract.return_value = {
        "user_role": "attorney",
        "user_id": "ATC82FA4EE",
        "start_str": "2024-01-01",
        "end_str": "2024-01-31"
    }

    mock_get_data.return_value = ([], [], 100)

    response = client.get('/api/payout/work-periods')
    assert response.status_code == 404
    assert response.get_json()["error"] == "No work periods found on that range."
