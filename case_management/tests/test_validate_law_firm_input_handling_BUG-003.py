import pytest
from unittest.mock import patch, MagicMock
from case_management.case_api import create_app
from case_management.models import LawFirm

@pytest.fixture
def client():
    app = create_app()
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client

# Test for /validate-law-firm-id/<int:law_firm_id> endpoint
class TestValidateLawFirmId:
    @patch('repositories.lawFirm.LawFirmRepository.get_law_firm_by_id')
    def test_validate_existing_law_firm_id(self, mock_get, client):
        # Setup mock to return a law firm
        mock_law_firm = MagicMock()
        mock_law_firm.id = 1
        mock_law_firm.name = "Test Firm"
        mock_get.return_value = mock_law_firm

        # Make request
        response = client.get('/validate-law-firm-id/1')

        # Assertions
        assert response.status_code == 200
        assert response.json == {
            "message": "Law firm ID is valid",
            "law_firm_id": 1,
            "law_firm_name": "Test Firm"
        }
        mock_get.assert_called_once_with(1)

    @patch('user_management.authentication.LawFirmRepository.get_law_firm_by_id')
    def test_validate_nonexistent_law_firm_id(self, mock_get, client):
        # Setup mock to return None (law firm not found)
        mock_get.return_value = None

        # Make request
        response = client.get('/validate-law-firm-id/999')

        # Assertions
        assert response.status_code == 404
        assert response.json == {"error": "Law firm ID not found"}
        mock_get.assert_called_once_with(999)

    @patch('user_management.authentication.LawFirmRepository.get_law_firm_by_id')
    def test_validate_law_firm_id_with_error(self, mock_get, client):
        # Setup mock to raise an exception
        mock_get.side_effect = Exception("Database error")

        # Make request
        response = client.get('/validate-law-firm-id/1')

        # Assertions
        assert response.status_code == 500
        assert "Unexpected error" in response.json["error"]
        mock_get.assert_called_once_with(1)

# Test for attorney registration with law firm ID validation
class TestAttorneyRegistrationWithLawFirmValidation:
    @patch('user_management.authentication.AttorneyRepository.create_attorney')
    @patch('user_management.authentication.LawFirmRepository.get_law_firm_by_id')
    @patch('user_management.authentication.AttorneyRepository.get_attorney_by_username')
    @patch('user_management.authentication.AttorneyRepository.get_attorney_by_email')
    def test_register_attorney_with_valid_law_firm(
        self, mock_get_email, mock_get_username, mock_get_law_firm, mock_create, client
    ):
        mock_get_law_firm.return_value = MagicMock(id=1, name="Test Law Firm")
        mock_get_username.return_value = None
        mock_get_email.return_value = None

        mock_attorney = MagicMock()
        mock_attorney.attorney_id = "AT123456"
        mock_create.return_value = mock_attorney

        test_data = {
            "username": "test_attorney",
            "password": "securepassword",
            "name": "Test Attorney",
            "email": "<EMAIL>",
            "role": "Associate",
            "hourly_rate": 200,
            "law_firm_id": 1
        }

        response = client.post('/register/attorney', json=test_data, content_type='application/json')
        print("Status code:", response.status_code)
        print("Response:", response.json)

        assert response.status_code == 201
        assert response.json["message"] == "Attorney registration successful"
        assert response.json["attorney_id"] == "AT123456"
        mock_get_law_firm.assert_called_once_with(1)

    @patch('user_management.authentication.LawFirmRepository.get_law_firm_by_id')
    def test_register_attorney_with_invalid_law_firm_id(self, mock_get, client):
        # Setup mock to return None (law firm not found)
        mock_get.return_value = None

        # Test data with invalid law firm ID
        test_data = {
            "username": "test_attorney",
            "password": "securepassword",
            "name": "Test Attorney",
            "email": "<EMAIL>",
            "role": "Associate",
            "hourly_rate": 200,
            "law_firm_id": "999"  # Non-existent ID
        }

        # Make request
        response = client.post(
            '/register/attorney',
            json=test_data,
            content_type='application/json'
        )

        # Assertions
        assert response.status_code == 400
        assert response.json == {"error": "Invalid law firm ID"}
        mock_get.assert_called_once_with(999)

    @patch('user_management.authentication.LawFirmRepository.get_law_firm_by_id')
    def test_register_attorney_with_non_numeric_law_firm_id(self, mock_get, client):
        # Test data with non-numeric law firm ID
        test_data = {
            "username": "test_attorney",
            "password": "securepassword",
            "name": "Test Attorney",
            "email": "<EMAIL>",
            "role": "Associate",
            "hourly_rate": 200,
            "law_firm_id": "abc"  # Invalid ID (not a number)
        }

        # Make request
        response = client.post(
            '/register/attorney',
            json=test_data,
            content_type='application/json'
        )

        # Assertions
        assert response.status_code == 400
        assert response.json == {"error": "Law firm ID must be a valid number"}
        mock_get.assert_not_called()  # Should fail before trying to get law firm

# Test for law firm name validation
class TestValidateLawFirmName:
    @patch('user_management.authentication.LawFirmRepository.get_law_firm_by_name')
    def test_validate_existing_law_firm_name(self, mock_get, client):
        # Setup mock to return a law firm
        mock_law_firm = MagicMock()
        mock_law_firm.id = 1
        mock_law_firm.name = "Existing Firm"
        mock_get.return_value = mock_law_firm

        # Make request
        response = client.get('/validate-law-firm/Existing%20Firm')

        # Assertions
        assert response.status_code == 200
        assert response.json == {
            "message": "Law firm is valid",
            "law_firm_id": 1,
            "law_firm_name": "Existing Firm"
        }
        mock_get.assert_called_once_with("Existing Firm")

    @patch('user_management.authentication.LawFirmRepository.get_law_firm_by_name')
    def test_validate_nonexistent_law_firm_name(self, mock_get, client):
        # Setup mock to return None (law firm not found)
        mock_get.return_value = None

        # Make request
        response = client.get('/validate-law-firm/Nonexistent%20Firm')

        # Assertions
        assert response.status_code == 404
        assert response.json == {"error": "Law firm not found"}
        mock_get.assert_called_once_with("Nonexistent Firm")