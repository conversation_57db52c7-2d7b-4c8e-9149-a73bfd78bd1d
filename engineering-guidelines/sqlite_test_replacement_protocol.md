# Procedure to Replace SQLite-Era Tests

## Purpose
To eliminate outdated tests written for SQLite assumptions and replace them with accurate, MySQL-compliant test cases aligned with production database behavior.

## Step-by-Step Instructions

### 1. Identify the Failing Test Functions
- Refer to your assigned test file and failing function list in the official test assignment sheet.

### 2. Open a Fix Branch
- Work within your assigned feature branch or a new dedicated branch for the test correction.

### 3. Remove Legacy Test Code
- Delete outdated or invalid test functions that fail due to SQLite-specific assumptions.

### 4. Write MySQL-Compliant Replacements
- Use valid test fixtures and ensure all test data respects foreign key constraints.
- Avoid mock setups that bypass relational integrity.

### 5. Match Test Names Where Possible
- Keep the original test function names **unless** the logic or structure must be significantly changed.
- This preserves Git history and makes diffs cleaner for reviewers.

### 6. Commit the Updated Tests
- Your commit should fully replace the SQLite-era tests.
- Ensure the logic aligns with actual MySQL behavior and schema.

### 7. Validate Locally
- Run tests via `pytest` using the Docker container or directly in CI.
- Ensure tests pass under MySQL constraints.

### 8. Submit a PR
- Title the PR:
```
[Test Fix] Replace outdated tests in <file_name>.py for MySQL compliance
```

---

## Notes of Caution

- **Do NOT patch SQLite-era tests.**  
  They are fundamentally flawed due to SQLite's weak enforcement of foreign key constraints.

- **This is a regime change.**  
  The old order must be purged. Replace the test suite cleanly and confidently.

---

Last Udpated
Stardate: 2025.183
The_Bossman_CLG 