# Rebase Protocol

**Effective Date:** Stardate 2025.185  
**Issued By:** The_Bossman_CLG

---

##  Purpose
To maintain stability and CI integrity, all developers must regularly rebase their feature branches with the latest changes from `main`.

---

##  Rebase Frequency (MANDATORY)

| When | Action |
|------|--------|
| Mid-shift | Rebase your feature branch to `origin/main` |
| End-of-shift | Rebase again to ensure full sync |
| After a PR is merged | Rebase immediately when PR is marked "closed" in `#sprint-tasks` |

---

##  Merge Signal

- All PR merge events are posted automatically in the `#sprint-tasks` channel.
- These messages (e.g., "PR #104 has been closed") **are your trigger to rebase**.

---

##  Rebase Instructions

git checkout <your-branch>
git fetch origin
git rebase origin/main
# Resolve any conflicts
git push --force-with-lease
