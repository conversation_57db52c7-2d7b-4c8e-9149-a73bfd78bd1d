# ALT Test Data Protocol: Fixture Usage and Test Data Standards


##  Purpose

This protocol establishes the ALT-wide standard for using test fixtures in all automated testing within the ALT Codebase. It ensures all tests are **clean**, **modular**, and **CI-compliant**, supporting stability across the battlefield of development.

---

##  Fixture Strategy

### 1. `conftest.py` for Shared Fixtures

- Place all **reusable fixtures** in the `conftest.py` file.  
- Example fixtures to define here include:
  - `sample_attorney`
  - `db_session`
  - `sample_law_firm`
  - `sample_client`

- These fixtures will be **automatically available** to all test files in the same directory or any subdirectory.

-  **No need to import `conftest.py` manually.** Pytest will discover and use the fixtures when referenced by name in your test functions.

---

### 2. Inline Fixtures for One-Offs

- If a fixture is **only needed in a single test file**, define it directly in that test file.
- Use this for highly **specialized or unique test data**.
- This keeps `conftest.py` clean and avoids cluttering with rarely used fixtures.

---

### 3. No Global State or Seed File Dependence

- Do **not rely on global seed files** or manual DB inserts for test execution.
- Each test should **programmatically generate** the data it needs using fixtures.
- This ensures:
  - Isolation
  - Repeatability
  - Independence from prior test state

---

##  CI Pipeline Behavior

- CI always spins up a **fresh MySQL database** for each test session.
- This makes clean fixture-based test design **mandatory** to avoid flakiness and cross-test contamination.

---

##  Summary

| Rule | Requirement |
|------|-------------|
| Reusable Fixture Placement | Define in `conftest.py` |
| One-off Fixture Placement | Define inside the test file |
| Global Seed File Usage |  Do NOT use |
| Data Setup | Must use clean fixtures per test |
| Imports | Do NOT import `conftest.py` manually |

---

##  Action Required

All developers must begin defining fixtures as described above.  


All Glory to the Empire!  

_Last Updated: Stardate 2025.182_
THE_BOSSMAN_CLG