# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.pyc


# C extensions
*.so

# Environment variables
database/.env
.env
.env.*

# Specifically allow example files
!*.env.example
!database/.env.example


# Ignore all BAT scripts in your private folder
bat files/*.bat

# Flask instance folder
instance/
!instance/cases.db   # ✅ Allow just the outer DB file


# Pytest cache
.pytest_cache/

# VSCode
.vscode/

# mypy
.mypy_cache/

# SQLite database
*.db
!instance/cases.db  # ✅ Exception again to be sure


#  EXCEPTION — allow this specific DB file
#!case_management/instance/cases.db

# Logs
*.log

# Docker
*.pid
*.pid.lock
docker-compose.override.yml

# Coverage reports
htmlcov/
.coverage
.coverage.*

# Build artifacts
build/
dist/
*.egg-info/

# Mac & system files
.DS_Store
Thumbs.db

# Ignore backup config files
Dockerfile.backup
docker-compose.yml.backup

node_modules/


# Ignore archived tests from being tracked
case_management/archived_tests/
altlc-pull-review-reports


*.iml
.gradle
/local.properties
/.idea/caches
/.idea/libraries
/.idea/modules.xml
/.idea/workspace.xml
/.idea/navEditor.xml
/.idea/assetWizardSettings.xml
.DS_Store
/build
/captures
.externalNativeBuild
.cxx
local.properties